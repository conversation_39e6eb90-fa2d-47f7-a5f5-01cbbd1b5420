#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
长江流域水质数据处理器 (1995-2004年)
用于将Word表格数据转换为CSV格式，便于数据分析和建模

作者：数学建模团队
日期：2025年
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')


class YangtzeWaterQualityProcessor:
    """长江流域水质数据处理类"""
    
    def __init__(self):
        """初始化处理器"""
        self.water_grades = ['Ⅰ类', 'Ⅱ类', 'Ⅲ类', 'Ⅳ类', 'Ⅴ类', '劣Ⅴ类']
        self.periods = ['枯水期', '丰水期', '水文年']
        self.regions = ['全流域', '干流', '支流']
        self.raw_data = {}
        self.processed_data = None
        
    def define_raw_data(self):
        """定义原始数据"""
        # 1995年数据
        self.raw_data[1995] = {
            '总流量': 9205,  # 亿立方米
            '废水排放': 174,  # 亿吨
            '枯水期': {
                '全流域': {'评价河长': 9925, 'Ⅰ类': (1452, 14.6), 'Ⅱ类': (5906, 59.5), 'Ⅲ类': (1879, 18.9), 'Ⅳ类': (268, 2.7), 'Ⅴ类': (170, 1.7), '劣Ⅴ类': (250, 2.5)},
                '干流': {'评价河长': 4456, 'Ⅰ类': (1216, 27.3), 'Ⅱ类': (2600, 58.3), 'Ⅲ类': (640, 14.4), 'Ⅳ类': (0, 0), 'Ⅴ类': (0, 0), '劣Ⅴ类': (0, 0)},
                '支流': {'评价河长': 5469, 'Ⅰ类': (236, 4.3), 'Ⅱ类': (3306, 60.5), 'Ⅲ类': (1239, 22.6), 'Ⅳ类': (268, 4.9), 'Ⅴ类': (170, 3.1), '劣Ⅴ类': (250, 4.6)}
            },
            '丰水期': {
                '全流域': {'评价河长': 9925, 'Ⅰ类': (2105, 21.2), 'Ⅱ类': (2871, 28.9), 'Ⅲ类': (3840, 38.7), 'Ⅳ类': (506, 5.1), 'Ⅴ类': (356, 3.6), '劣Ⅴ类': (247, 2.5)},
                '干流': {'评价河长': 4456, 'Ⅰ类': (668, 15.0), 'Ⅱ类': (789, 17.7), 'Ⅲ类': (2182, 48.9), 'Ⅳ类': (390, 8.7), 'Ⅴ类': (248, 5.6), '劣Ⅴ类': (179, 4.0)},
                '支流': {'评价河长': 5469, 'Ⅰ类': (1437, 26.3), 'Ⅱ类': (2082, 38.0), 'Ⅲ类': (1658, 30.3), 'Ⅳ类': (116, 2.1), 'Ⅴ类': (108, 2.0), '劣Ⅴ类': (68, 1.2)}
            },
            '水文年': {
                '全流域': {'评价河长': 9925, 'Ⅰ类': (2564, 25.8), 'Ⅱ类': (4225, 42.6), 'Ⅲ类': (2452, 24.7), 'Ⅳ类': (387, 3.9), 'Ⅴ类': (297, 3.0), '劣Ⅴ类': (0, 0)},
                '干流': {'评价河长': 4456, 'Ⅰ类': (1101, 24.7), 'Ⅱ类': (1590, 35.7), 'Ⅲ类': (1338, 30), 'Ⅳ类': (130, 2.9), 'Ⅴ类': (297, 6.7), '劣Ⅴ类': (0, 0)},
                '支流': {'评价河长': 5469, 'Ⅰ类': (1463, 26.7), 'Ⅱ类': (2635, 48.2), 'Ⅲ类': (1114, 20.4), 'Ⅳ类': (257, 4.7), 'Ⅴ类': (0, 0), '劣Ⅴ类': (0, 0)}
            }
        }
        
        # 1996年数据
        self.raw_data[1996] = {
            '总流量': 9513,
            '废水排放': 179,
            '枯水期': {
                '全流域': {'评价河长': 10172, 'Ⅰ类': (1242, 12.2), 'Ⅱ类': (1650, 16.2), 'Ⅲ类': (5534, 54.4), 'Ⅳ类': (961, 9.5), 'Ⅴ类': (387, 3.8), '劣Ⅴ类': (397, 3.9)},
                '干流': {'评价河长': 4479, 'Ⅰ类': (418, 9.3), 'Ⅱ类': (811, 18.1), 'Ⅲ类': (3250, 72.6), 'Ⅳ类': (0, 0), 'Ⅴ类': (0, 0), '劣Ⅴ类': (0, 0)},
                '支流': {'评价河长': 5693, 'Ⅰ类': (824, 14.5), 'Ⅱ类': (839, 14.7), 'Ⅲ类': (2284, 40.1), 'Ⅳ类': (961, 16.9), 'Ⅴ类': (387, 6.8), '劣Ⅴ类': (397, 7)}
            },
            '丰水期': {
                '全流域': {'评价河长': 10037, 'Ⅰ类': (1456, 14.5), 'Ⅱ类': (2523, 25.1), 'Ⅲ类': (4612, 45.9), 'Ⅳ类': (1017, 10.1), 'Ⅴ类': (151, 1.5), '劣Ⅴ类': (278, 2.8)},
                '干流': {'评价河长': 4479, 'Ⅰ类': (931, 20.8), 'Ⅱ类': (1817, 40.5), 'Ⅲ类': (1614, 36.1), 'Ⅳ类': (0, 0), 'Ⅴ类': (49, 1.1), '劣Ⅴ类': (68, 1.5)},
                '支流': {'评价河长': 5558, 'Ⅰ类': (525, 9.5), 'Ⅱ类': (706, 12.7), 'Ⅲ类': (2998, 53.9), 'Ⅳ类': (1017, 18.3), 'Ⅴ类': (102, 1.8), '劣Ⅴ类': (210, 3.8)}
            },
            '水文年': {
                '全流域': {'评价河长': 10037, 'Ⅰ类': (1533, 15.3), 'Ⅱ类': (2030, 20.2), 'Ⅲ类': (4998, 49.8), 'Ⅳ类': (974, 9.7), 'Ⅴ类': (191, 1.9), '劣Ⅴ类': (311, 3.1)},
                '干流': {'评价河长': 4479, 'Ⅰ类': (1146, 25.6), 'Ⅱ类': (1322, 29.5), 'Ⅲ类': (1975, 44.1), 'Ⅳ类': (0, 0), 'Ⅴ类': (36, 0.8), '劣Ⅴ类': (0, 0)},
                '支流': {'评价河长': 5558, 'Ⅰ类': (387, 7.0), 'Ⅱ类': (708, 12.7), 'Ⅲ类': (3023, 54.4), 'Ⅳ类': (974, 17.5), 'Ⅴ类': (155, 2.8), '劣Ⅴ类': (311, 5.6)}
            }
        }
        
        # 1997年数据
        self.raw_data[1997] = {
            '总流量': 9171.26,
            '废水排放': 183,
            '枯水期': {
                '全流域': {'评价河长': 9992, 'Ⅰ类': (1045, 10.5), 'Ⅱ类': (1363, 13.6), 'Ⅲ类': (4313, 43.2), 'Ⅳ类': (2599, 26.0), 'Ⅴ类': (319.7, 3.2), '劣Ⅴ类': (349.7, 3.5)},
                '干流': {'评价河长': 4479, 'Ⅰ类': (560, 12.5), 'Ⅱ类': (781, 17.4), 'Ⅲ类': (1926, 43), 'Ⅳ类': (1212, 27.1), 'Ⅴ类': (0, 0), '劣Ⅴ类': (0, 0)},
                '支流': {'评价河长': 5513, 'Ⅰ类': (485, 8.8), 'Ⅱ类': (582, 10.5), 'Ⅲ类': (2387, 43.3), 'Ⅳ类': (1387, 25.2), 'Ⅴ类': (319.7, 5.8), '劣Ⅴ类': (349.7, 6.3)}
            },
            '丰水期': {
                '全流域': {'评价河长': 9992, 'Ⅰ类': (1468, 14.7), 'Ⅱ类': (2895, 28.9), 'Ⅲ类': (4405, 44.1), 'Ⅳ类': (816, 8.2), 'Ⅴ类': (99.1, 1), '劣Ⅴ类': (309.7, 3.1)},
                '干流': {'评价河长': 4479, 'Ⅰ类': (875, 19.6), 'Ⅱ类': (1492, 33.3), 'Ⅲ类': (2112, 47.1), 'Ⅳ类': (0, 0), 'Ⅴ类': (0, 0), '劣Ⅴ类': (0, 0)},
                '支流': {'评价河长': 5513, 'Ⅰ类': (593, 10.7), 'Ⅱ类': (1403, 25.5), 'Ⅲ类': (2293, 41.6), 'Ⅳ类': (816, 14.8), 'Ⅴ类': (99.1, 1.8), '劣Ⅴ类': (309.7, 5.6)}
            },
            '水文年': {
                '全流域': {'评价河长': 9992, 'Ⅰ类': (1225, 12.2), 'Ⅱ类': (2582, 24.9), 'Ⅲ类': (4356.5, 43.6), 'Ⅳ类': (1328.9, 13.3), 'Ⅴ类': (259.8, 2.6), '劣Ⅴ类': (339.7, 3.4)},
                '干流': {'评价河长': 4479, 'Ⅰ类': (654, 14.6), 'Ⅱ类': (1236, 27.6), 'Ⅲ类': (1993.1, 44.5), 'Ⅳ类': (595.7, 13.3), 'Ⅴ类': (0, 0), '劣Ⅴ类': (0, 0)},
                '支流': {'评价河长': 5513, 'Ⅰ类': (671, 12.2), 'Ⅱ类': (1346, 20.8), 'Ⅲ类': (2363.4, 42.9), 'Ⅳ类': (733.2, 13.3), 'Ⅴ类': (259.8, 4.7), '劣Ⅴ类': (339.7, 6.2)}
            }
        }

        # 1998年数据
        self.raw_data[1998] = {
            '总流量': 13127,
            '废水排放': 189,
            '枯水期': {
                '全流域': {'评价河长': 10958, 'Ⅰ类': (1520, 13.9), 'Ⅱ类': (2810, 25.6), 'Ⅲ类': (5059, 46.2), 'Ⅳ类': (897, 8.2), 'Ⅴ类': (300, 2.7), '劣Ⅴ类': (372, 3.4)},
                '干流': {'评价河长': 4529, 'Ⅰ类': (534, 11.8), 'Ⅱ类': (927, 20.5), 'Ⅲ类': (3068, 67.7), 'Ⅳ类': (0, 0), 'Ⅴ类': (0, 0), '劣Ⅴ类': (0, 0)},
                '支流': {'评价河长': 6429, 'Ⅰ类': (986, 15.3), 'Ⅱ类': (1883, 29.3), 'Ⅲ类': (1991, 31.0), 'Ⅳ类': (897, 14.0), 'Ⅴ类': (300, 2.7), '劣Ⅴ类': (372, 3.4)}
            },
            '丰水期': {
                '全流域': {'评价河长': 10958, 'Ⅰ类': (540, 4.9), 'Ⅱ类': (1825, 16.7), 'Ⅲ类': (7274, 66.4), 'Ⅳ类': (780, 7.1), 'Ⅴ类': (330, 3.0), '劣Ⅴ类': (309, 2.8)},
                '干流': {'评价河长': 4529, 'Ⅰ类': (124, 2.7), 'Ⅱ类': (368, 8.2), 'Ⅲ类': (3847, 84.9), 'Ⅳ类': (0, 0), 'Ⅴ类': (190, 4.2), '劣Ⅴ类': (0, 0)},
                '支流': {'评价河长': 6429, 'Ⅰ类': (416, 6.5), 'Ⅱ类': (1457, 22.6), 'Ⅲ类': (3427, 53.3), 'Ⅳ类': (780, 12.2), 'Ⅴ类': (140, 2.2), '劣Ⅴ类': (309, 4.8)}
            },
            '水文年': {
                '全流域': {'评价河长': 10958, 'Ⅰ类': (1256, 11.5), 'Ⅱ类': (2645, 24.1), 'Ⅲ类': (5786, 52.8), 'Ⅳ类': (905, 8.3), 'Ⅴ类': (186, 1.7), '劣Ⅴ类': (180, 1.6)},
                '干流': {'评价河长': 4529, 'Ⅰ类': (465, 10.3), 'Ⅱ类': (910, 20.1), 'Ⅲ类': (3154, 69.6), 'Ⅳ类': (0, 0), 'Ⅴ类': (0, 0), '劣Ⅴ类': (0, 0)},
                '支流': {'评价河长': 6429, 'Ⅰ类': (791, 12.3), 'Ⅱ类': (1735, 27.0), 'Ⅲ类': (2632, 40.9), 'Ⅳ类': (905, 14.1), 'Ⅴ类': (186, 2.9), '劣Ⅴ类': (180, 2.8)}
            }
        }

        # 1999年数据
        self.raw_data[1999] = {
            '总流量': 9513,
            '废水排放': 207,
            '枯水期': {
                '全流域': {'评价河长': 30466, 'Ⅰ类': (1522, 5.0), 'Ⅱ类': (12714, 41.7), 'Ⅲ类': (9220, 30.3), 'Ⅳ类': (3778, 12.4), 'Ⅴ类': (1493, 4.9), '劣Ⅴ类': (1739, 5.7)},
                '干流': {'评价河长': 6136, 'Ⅰ类': (0, 0), 'Ⅱ类': (3229, 52.6), 'Ⅲ类': (2119, 34.5), 'Ⅳ类': (649, 10.6), 'Ⅴ类': (139, 2.3), '劣Ⅴ类': (0, 0)},
                '支流': {'评价河长': 24330, 'Ⅰ类': (1522, 6.2), 'Ⅱ类': (9485, 39), 'Ⅲ类': (7101, 29.2), 'Ⅳ类': (3129, 12.9), 'Ⅴ类': (1354, 5.6), '劣Ⅴ类': (1739, 7.1)}
            },
            '丰水期': {
                '全流域': {'评价河长': 30466, 'Ⅰ类': (1601, 5.3), 'Ⅱ类': (12348, 40.5), 'Ⅲ类': (10465, 34.3), 'Ⅳ类': (3103, 10.2), 'Ⅴ类': (1279, 4.2), '劣Ⅴ类': (1670, 5.5)},
                '干流': {'评价河长': 6136, 'Ⅰ类': (0, 0), 'Ⅱ类': (3590, 58.5), 'Ⅲ类': (1897, 30.9), 'Ⅳ类': (649, 10.6), 'Ⅴ类': (0, 0), '劣Ⅴ类': (0, 0)},
                '支流': {'评价河长': 24330, 'Ⅰ类': (1601, 6.6), 'Ⅱ类': (8758, 36), 'Ⅲ类': (8568, 35.2), 'Ⅳ类': (2454, 10.1), 'Ⅴ类': (1279, 5.2), '劣Ⅴ类': (1670, 6.9)}
            },
            '水文年': {
                '全流域': {'评价河长': 30466, 'Ⅰ类': (1571, 5.2), 'Ⅱ类': (12133, 39.8), 'Ⅲ类': (10738, 35.2), 'Ⅳ类': (2888, 9.5), 'Ⅴ类': (1897, 6.2), '劣Ⅴ类': (1239, 4.1)},
                '干流': {'评价河长': 6136, 'Ⅰ类': (0, 0), 'Ⅱ类': (3459, 56.4), 'Ⅲ类': (1889, 30.8), 'Ⅳ类': (340, 5.5), 'Ⅴ类': (448, 7.3), '劣Ⅴ类': (0, 0)},
                '支流': {'评价河长': 24330, 'Ⅰ类': (1571, 6.5), 'Ⅱ类': (8674, 34.5), 'Ⅲ类': (8849, 35.2), 'Ⅳ类': (2548, 10.4), 'Ⅴ类': (1449, 6.0), '劣Ⅴ类': (1239, 5.1)}
            }
        }

        # 2000年数据
        self.raw_data[2000] = {
            '总流量': 9924,
            '废水排放': 234,
            '枯水期': {
                '全流域': {'评价河长': 30312, 'Ⅰ类': (2402, 7.9), 'Ⅱ类': (10681, 35.2), 'Ⅲ类': (9093, 30), 'Ⅳ类': (4530, 14.9), 'Ⅴ类': (1797, 5.9), '劣Ⅴ类': (1809, 6)},
                '干流': {'评价河长': 5285, 'Ⅰ类': (850, 16.1), 'Ⅱ类': (1692, 32), 'Ⅲ类': (1398, 26.5), 'Ⅳ类': (1345, 25.4), 'Ⅴ类': (0, 0), '劣Ⅴ类': (0, 0)},
                '支流': {'评价河长': 25027, 'Ⅰ类': (1552, 6.2), 'Ⅱ类': (8989, 35.9), 'Ⅲ类': (7695, 30.8), 'Ⅳ类': (3185, 12.7), 'Ⅴ类': (1797, 7.2), '劣Ⅴ类': (1809, 7.2)}
            },
            '丰水期': {
                '全流域': {'评价河长': 30312, 'Ⅰ类': (1729, 5.7), 'Ⅱ类': (10001, 33), 'Ⅲ类': (11507, 38), 'Ⅳ类': (4489, 14.8), 'Ⅴ类': (1258, 4.1), '劣Ⅴ类': (1328, 4.4)},
                '干流': {'评价河长': 5285, 'Ⅰ类': (500, 9.5), 'Ⅱ类': (1772, 33.5), 'Ⅲ类': (1668, 31.6), 'Ⅳ类': (1345, 25.4), 'Ⅴ类': (0, 0), '劣Ⅴ类': (0, 0)},
                '支流': {'评价河长': 25027, 'Ⅰ类': (1229, 4.9), 'Ⅱ类': (8229, 32.9), 'Ⅲ类': (9839, 39.3), 'Ⅳ类': (3144, 12.6), 'Ⅴ类': (1258, 5), '劣Ⅴ类': (1328, 5.3)}
            },
            '水文年': {
                '全流域': {'评价河长': 30312, 'Ⅰ类': (1705, 5.6), 'Ⅱ类': (9944, 32.8), 'Ⅲ类': (10792, 35.6), 'Ⅳ类': (5037, 16.6), 'Ⅴ类': (1320, 4.4), '劣Ⅴ类': (1608, 5.3)},
                '干流': {'评价河长': 5285, 'Ⅰ类': (500, 9.5), 'Ⅱ类': (1899, 35.9), 'Ⅲ类': (1541, 29.1), 'Ⅳ类': (1345, 25.4), 'Ⅴ类': (0, 0), '劣Ⅴ类': (0, 0)},
                '支流': {'评价河长': 25027, 'Ⅰ类': (1205, 4.8), 'Ⅱ类': (8045, 32.2), 'Ⅲ类': (9251, 37), 'Ⅳ类': (3692, 14.8), 'Ⅴ类': (1320, 5.3), '劣Ⅴ类': (1608, 6.4)}
            }
        }

        # 2001年数据
        self.raw_data[2001] = {
            '总流量': 8892.8,
            '废水排放': 220.5,
            '枯水期': {
                '全流域': {'评价河长': 34146, 'Ⅰ类': (307.3, 0.9), 'Ⅱ类': (11712, 34.3), 'Ⅲ类': (12327, 36.1), 'Ⅳ类': (5122, 15), 'Ⅴ类': (2151, 6.3), '劣Ⅴ类': (2527, 7.4)},
                '干流': {'评价河长': 6012, 'Ⅰ类': (222, 3.7), 'Ⅱ类': (1641, 27.3), 'Ⅲ类': (2158, 35.9), 'Ⅳ类': (1148, 19.1), 'Ⅴ类': (487, 8.1), '劣Ⅴ类': (415, 6.9)},
                '支流': {'评价河长': 28134, 'Ⅰ类': (85.3, 0.3), 'Ⅱ类': (10071, 35.8), 'Ⅲ类': (10169, 36.1), 'Ⅳ类': (3974, 14.1), 'Ⅴ类': (1664, 5.9), '劣Ⅴ类': (2112, 7.5)}
            },
            '丰水期': {
                '全流域': {'评价河长': 34146, 'Ⅰ类': (2424, 7.1), 'Ⅱ类': (11200, 32.8), 'Ⅲ类': (12736, 37.3), 'Ⅳ类': (4678, 13.7), 'Ⅴ类': (1332, 3.9), '劣Ⅴ类': (1776, 5.2)},
                '干流': {'评价河长': 6012, 'Ⅰ类': (36, 0.6), 'Ⅱ类': (2050, 34.1), 'Ⅲ类': (2297, 38.2), 'Ⅳ类': (848, 14.1), 'Ⅴ类': (475, 7.9), '劣Ⅴ类': (307, 5.1)},
                '支流': {'评价河长': 28134, 'Ⅰ类': (2388, 8.5), 'Ⅱ类': (9150, 32.5), 'Ⅲ类': (10439, 37.1), 'Ⅳ类': (3830, 13.6), 'Ⅴ类': (857, 3.1), '劣Ⅴ类': (1469, 5.2)}
            },
            '水文年': {
                '全流域': {'评价河长': 34146, 'Ⅰ类': (2013, 5.9), 'Ⅱ类': (11289, 33.1), 'Ⅲ类': (11855, 34.7), 'Ⅳ类': (4784, 14), 'Ⅴ类': (1876, 5.5), '劣Ⅴ类': (2329, 6.8)},
                '干流': {'评价河长': 6012, 'Ⅰ类': (138, 2.3), 'Ⅱ类': (1810, 30.1), 'Ⅲ类': (2122, 35.3), 'Ⅳ类': (1124, 18.7), 'Ⅴ类': (469, 7.8), '劣Ⅴ类': (349, 5.8)},
                '支流': {'评价河长': 28134, 'Ⅰ类': (1875, 6.7), 'Ⅱ类': (9479, 33.7), 'Ⅲ类': (9733, 34.6), 'Ⅳ类': (3660, 13), 'Ⅴ类': (1407, 5), '劣Ⅴ类': (1980, 7)}
            }
        }

        # 2002年数据
        self.raw_data[2002] = {
            '总流量': 10210,
            '废水排放': 256,
            '枯水期': {
                '全流域': {'评价河长': 35386, 'Ⅰ类': (283, 0.8), 'Ⅱ类': (12633, 35.7), 'Ⅲ类': (11005, 31.1), 'Ⅳ类': (5697, 16.1), 'Ⅴ类': (1026, 2.9), '劣Ⅴ类': (4742, 13.4)},
                '干流': {'评价河长': 5983, 'Ⅰ类': (0, 0), 'Ⅱ类': (1580, 26.4), 'Ⅲ类': (2220, 37.1), 'Ⅳ类': (1221, 20.4), 'Ⅴ类': (485, 8.1), '劣Ⅴ类': (473, 7.9)},
                '支流': {'评价河长': 29403, 'Ⅰ类': (283, 1), 'Ⅱ类': (11053, 37.6), 'Ⅲ类': (8785, 30), 'Ⅳ类': (4476, 15.2), 'Ⅴ类': (541, 1.8), '劣Ⅴ类': (4269, 14.5)}
            },
            '丰水期': {
                '全流域': {'评价河长': 35386, 'Ⅰ类': (2300, 6.5), 'Ⅱ类': (9943, 28.1), 'Ⅲ类': (14190, 40.1), 'Ⅳ类': (4919, 13.9), 'Ⅴ类': (2194, 6.2), '劣Ⅴ类': (1840, 5.2)},
                '干流': {'评价河长': 5983, 'Ⅰ类': (90, 1.5), 'Ⅱ类': (1669, 27.9), 'Ⅲ类': (2483, 41.5), 'Ⅳ类': (909, 15.2), 'Ⅴ类': (545, 9.1), '劣Ⅴ类': (287, 4.8)},
                '支流': {'评价河长': 29403, 'Ⅰ类': (2210, 7.5), 'Ⅱ类': (8274, 28.1), 'Ⅲ类': (11707, 39.8), 'Ⅳ类': (4010, 13.6), 'Ⅴ类': (1649, 5.6), '劣Ⅴ类': (1553, 5.3)}
            },
            '水文年': {
                '全流域': {'评价河长': 35386, 'Ⅰ类': (1572, 4.4), 'Ⅱ类': (15574, 44), 'Ⅲ类': (10022, 28.3), 'Ⅳ类': (3544, 10.0), 'Ⅴ类': (1136, 3.2), '劣Ⅴ类': (3538, 10.0)},
                '干流': {'评价河长': 5983, 'Ⅰ类': (185, 3.1), 'Ⅱ类': (2118, 35.4), 'Ⅲ类': (1813, 30.3), 'Ⅳ类': (1041, 17.4), 'Ⅴ类': (305, 5.1), '劣Ⅴ类': (521, 8.7)},
                '支流': {'评价河长': 29403, 'Ⅰ类': (1387, 4.7), 'Ⅱ类': (13456, 45.7), 'Ⅲ类': (8209, 27.9), 'Ⅳ类': (2503, 8.5), 'Ⅴ类': (831, 2.8), '劣Ⅴ类': (3017, 10.3)}
            }
        }

        # 2003年数据
        self.raw_data[2003] = {
            '总流量': 9980,
            '废水排放': 270,
            '枯水期': {
                '全流域': {'评价河长': 38513, 'Ⅰ类': (501.1, 1.3), 'Ⅱ类': (11169, 29.5), 'Ⅲ类': (16060, 41.7), 'Ⅳ类': (3697, 9.6), 'Ⅴ类': (1309, 3.4), '劣Ⅴ类': (5584, 14.5)},
                '干流': {'评价河长': 6226, 'Ⅰ类': (131, 2.1), 'Ⅱ类': (1295, 20.8), 'Ⅲ类': (2503, 40.2), 'Ⅳ类': (1718, 27.6), 'Ⅴ类': (579, 9.3), '劣Ⅴ类': (0, 0)},
                '支流': {'评价河长': 32287, 'Ⅰ类': (4880, 5.1), 'Ⅱ类': (9874, 28.9), 'Ⅲ类': (13557, 40.3), 'Ⅳ类': (1979, 6.1), 'Ⅴ类': (730, 2.3), '劣Ⅴ类': (5584, 17.3)}
            },
            '丰水期': {
                '全流域': {'评价河长': 38513, 'Ⅰ类': (809, 2.1), 'Ⅱ类': (10938, 28.4), 'Ⅲ类': (14288, 37.1), 'Ⅳ类': (7664, 19.9), 'Ⅴ类': (3120, 8.1), '劣Ⅴ类': (1772, 4.6)},
                '干流': {'评价河长': 6226, 'Ⅰ类': (118, 1.9), 'Ⅱ类': (2123, 34.1), 'Ⅲ类': (2472, 39.7), 'Ⅳ类': (1108, 17.8), 'Ⅴ类': (405, 6.5), '劣Ⅴ类': (0, 0)},
                '支流': {'评价河长': 32287, 'Ⅰ类': (691, 2.1), 'Ⅱ类': (8815, 27.3), 'Ⅲ类': (11816, 36.6), 'Ⅳ类': (6556, 20.3), 'Ⅴ类': (2715, 8.4), '劣Ⅴ类': (1772, 5.5)}
            },
            '水文年': {
                '全流域': {'评价河长': 38513, 'Ⅰ类': (1824, 4.7), 'Ⅱ类': (15964, 41.5), 'Ⅲ类': (12048, 31.3), 'Ⅳ类': (2458, 6.4), 'Ⅴ类': (2246, 5.8), '劣Ⅴ类': (3973, 10.3)},
                '干流': {'评价河长': 6226, 'Ⅰ类': (500.0, 8), 'Ⅱ类': (1107, 17.8), 'Ⅲ类': (4236, 68), 'Ⅳ类': (94, 1.5), 'Ⅴ类': (289, 4.6), '劣Ⅴ类': (0, 0)},
                '支流': {'评价河长': 32287, 'Ⅰ类': (1324, 4.1), 'Ⅱ类': (14857, 46), 'Ⅲ类': (7812, 24.2), 'Ⅳ类': (2364, 7.3), 'Ⅴ类': (1957, 6.1), '劣Ⅴ类': (3973, 12.3)}
            }
        }

        # 2004年数据
        self.raw_data[2004] = {
            '总流量': 9405,
            '废水排放': 285,
            '枯水期': {
                '全流域': {'评价河长': 39412, 'Ⅰ类': (354, 0.9), 'Ⅱ类': (10525, 26.7), 'Ⅲ类': (15843, 40.2), 'Ⅳ类': (5951, 15.1), 'Ⅴ类': (2049, 5.2), '劣Ⅴ类': (4690, 11.9)},
                '干流': {'评价河长': 6341, 'Ⅰ类': (70, 1.1), 'Ⅱ类': (1592, 25.1), 'Ⅲ类': (2479, 39.1), 'Ⅳ类': (704, 11.1), 'Ⅴ类': (596, 9.4), '劣Ⅴ类': (900, 14.2)},
                '支流': {'评价河长': 33071, 'Ⅰ类': (284, 0.9), 'Ⅱ类': (8933, 27), 'Ⅲ类': (13364, 40.4), 'Ⅳ类': (5247, 15.9), 'Ⅴ类': (1453, 4.4), '劣Ⅴ类': (3790, 11.4)}
            },
            '丰水期': {
                '全流域': {'评价河长': 39412, 'Ⅰ类': (551, 1.4), 'Ⅱ类': (10681, 27.1), 'Ⅲ类': (15607, 39.6), 'Ⅳ类': (5794, 14.7), 'Ⅴ类': (2641, 6.7), '劣Ⅴ类': (4138, 10.5)},
                '干流': {'评价河长': 6341, 'Ⅰ类': (82, 1.3), 'Ⅱ类': (1535, 24.2), 'Ⅲ类': (2632, 41.5), 'Ⅳ类': (1033, 16.3), 'Ⅴ类': (469, 7.4), '劣Ⅴ类': (590, 9.3)},
                '支流': {'评价河长': 33071, 'Ⅰ类': (469, 1.4), 'Ⅱ类': (9146, 27.7), 'Ⅲ类': (12975, 39.2), 'Ⅳ类': (4761, 14.4), 'Ⅴ类': (2172, 6.6), '劣Ⅴ类': (3548, 10.7)}
            },
            '水文年': {
                '全流域': {'评价河长': 39412, 'Ⅰ类': (473, 1.2), 'Ⅱ类': (10602, 26.9), 'Ⅲ类': (15725, 39.9), 'Ⅳ类': (5833, 14.8), 'Ⅴ类': (2325, 5.9), '劣Ⅴ类': (4454, 11.3)},
                '干流': {'评价河长': 6341, 'Ⅰ类': (70, 1.1), 'Ⅱ类': (1636, 25.8), 'Ⅲ类': (2574, 40.6), 'Ⅳ类': (995, 15.7), 'Ⅴ类': (495, 7.8), '劣Ⅴ类': (571, 9.0)},
                '支流': {'评价河长': 33071, 'Ⅰ类': (403, 1.2), 'Ⅱ类': (8966, 27.1), 'Ⅲ类': (13151, 39.8), 'Ⅳ类': (4838, 14.6), 'Ⅴ类': (1830, 5.5), '劣Ⅴ类': (3883, 11.7)}
            }
        }

    def validate_data(self) -> Dict[str, List[str]]:
        """
        验证数据完整性和一致性

        Returns:
            Dict[str, List[str]]: 验证结果，包含错误和警告信息
        """
        validation_results = {'errors': [], 'warnings': []}

        for year, year_data in self.raw_data.items():
            for period in self.periods:
                if period not in year_data:
                    validation_results['errors'].append(f"{year}年缺少{period}数据")
                    continue

                for region in self.regions:
                    if region not in year_data[period]:
                        validation_results['errors'].append(f"{year}年{period}{region}数据缺失")
                        continue

                    region_data = year_data[period][region]
                    eval_length = region_data.get('评价河长', 0)

                    # 检查河长总和
                    total_length = sum(region_data[grade][0] for grade in self.water_grades if grade in region_data)
                    if abs(total_length - eval_length) > 1:  # 允许1km的误差
                        validation_results['warnings'].append(
                            f"{year}年{period}{region}: 河长总和({total_length:.1f})与评价河长({eval_length})不匹配"
                        )

                    # 检查百分比总和
                    total_percentage = sum(region_data[grade][1] for grade in self.water_grades if grade in region_data)
                    if abs(total_percentage - 100) > 1:  # 允许1%的误差
                        validation_results['warnings'].append(
                            f"{year}年{period}{region}: 百分比总和({total_percentage:.1f}%)不等于100%"
                        )

        return validation_results

    def process_to_long_format(self) -> pd.DataFrame:
        """
        将数据转换为长表格式

        Returns:
            pd.DataFrame: 长表格式的数据
        """
        records = []

        for year, year_data in self.raw_data.items():
            total_flow = year_data.get('总流量', None)
            waste_discharge = year_data.get('废水排放', None)

            for period in self.periods:
                if period not in year_data:
                    continue

                for region in self.regions:
                    if region not in year_data[period]:
                        continue

                    region_data = year_data[period][region]
                    eval_length = region_data.get('评价河长', 0)

                    for grade in self.water_grades:
                        if grade in region_data:
                            length, percentage = region_data[grade]
                            records.append({
                                '年份': year,
                                '时段': period,
                                '范围': region,
                                '评价河长_km': eval_length,
                                '水质等级': grade,
                                '河长_km': length,
                                '百分比_%': percentage,
                                '总流量_亿立方米': total_flow,
                                '废水排放_亿吨': waste_discharge
                            })

        return pd.DataFrame(records)

    def process_to_wide_format(self) -> pd.DataFrame:
        """
        将数据转换为宽表格式

        Returns:
            pd.DataFrame: 宽表格式的数据
        """
        records = []

        for year, year_data in self.raw_data.items():
            total_flow = year_data.get('总流量', None)
            waste_discharge = year_data.get('废水排放', None)

            for period in self.periods:
                if period not in year_data:
                    continue

                for region in self.regions:
                    if region not in year_data[period]:
                        continue

                    region_data = year_data[period][region]
                    eval_length = region_data.get('评价河长', 0)

                    record = {
                        '年份': year,
                        '时段': period,
                        '范围': region,
                        '评价河长_km': eval_length,
                        '总流量_亿立方米': total_flow,
                        '废水排放_亿吨': waste_discharge
                    }

                    # 添加各水质等级的河长和百分比
                    for grade in self.water_grades:
                        if grade in region_data:
                            length, percentage = region_data[grade]
                            record[f'{grade}_河长_km'] = length
                            record[f'{grade}_百分比_%'] = percentage
                        else:
                            record[f'{grade}_河长_km'] = 0
                            record[f'{grade}_百分比_%'] = 0

                    records.append(record)

        return pd.DataFrame(records)

    def generate_summary_statistics(self, df_long: pd.DataFrame) -> pd.DataFrame:
        """
        生成汇总统计信息

        Args:
            df_long: 长表格式的数据

        Returns:
            pd.DataFrame: 统计汇总表
        """
        # 按年份和水质等级汇总
        summary = df_long.groupby(['年份', '水质等级']).agg({
            '河长_km': 'sum',
            '百分比_%': 'mean',
            '总流量_亿立方米': 'first',
            '废水排放_亿吨': 'first'
        }).reset_index()

        # 计算水质优良率（Ⅰ-Ⅲ类水质比例）
        good_quality = ['Ⅰ类', 'Ⅱ类', 'Ⅲ类']
        yearly_stats = []

        for year in sorted(df_long['年份'].unique()):
            year_data = df_long[df_long['年份'] == year]
            total_length = year_data['河长_km'].sum()
            good_length = year_data[year_data['水质等级'].isin(good_quality)]['河长_km'].sum()
            good_ratio = (good_length / total_length * 100) if total_length > 0 else 0

            yearly_stats.append({
                '年份': year,
                '总河长_km': total_length,
                '优良水质河长_km': good_length,
                '水质优良率_%': good_ratio,
                '总流量_亿立方米': year_data['总流量_亿立方米'].iloc[0],
                '废水排放_亿吨': year_data['废水排放_亿吨'].iloc[0]
            })

        return pd.DataFrame(yearly_stats)

    def export_to_csv(self, output_dir: str = '.') -> Dict[str, str]:
        """
        导出数据到CSV文件

        Args:
            output_dir: 输出目录

        Returns:
            Dict[str, str]: 生成的文件路径
        """
        import os

        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 处理数据
        df_long = self.process_to_long_format()
        df_wide = self.process_to_wide_format()
        df_summary = self.generate_summary_statistics(df_long)

        # 导出文件
        files = {}

        # 长表格式
        long_path = os.path.join(output_dir, 'yangtze_water_quality_1995_2004_long.csv')
        df_long.to_csv(long_path, index=False, encoding='utf-8-sig')
        files['long_format'] = long_path

        # 宽表格式
        wide_path = os.path.join(output_dir, 'yangtze_water_quality_1995_2004_wide.csv')
        df_wide.to_csv(wide_path, index=False, encoding='utf-8-sig')
        files['wide_format'] = wide_path

        # 汇总统计
        summary_path = os.path.join(output_dir, 'yangtze_water_quality_1995_2004_summary.csv')
        df_summary.to_csv(summary_path, index=False, encoding='utf-8-sig')
        files['summary'] = summary_path

        return files

    def generate_data_quality_report(self, output_dir: str = '.') -> str:
        """
        生成数据质量报告

        Args:
            output_dir: 输出目录

        Returns:
            str: 报告文件路径
        """
        import os
        from datetime import datetime

        validation_results = self.validate_data()
        df_long = self.process_to_long_format()

        report_path = os.path.join(output_dir, 'data_quality_report.txt')

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("长江流域水质数据质量报告\n")
            f.write("=" * 50 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"数据时间范围: 1995-2004年\n\n")

            # 数据概览
            f.write("数据概览:\n")
            f.write("-" * 20 + "\n")
            f.write(f"总记录数: {len(df_long)}\n")
            f.write(f"年份数量: {df_long['年份'].nunique()}\n")
            f.write(f"时段类型: {', '.join(df_long['时段'].unique())}\n")
            f.write(f"评价范围: {', '.join(df_long['范围'].unique())}\n")
            f.write(f"水质等级: {', '.join(df_long['水质等级'].unique())}\n\n")

            # 验证结果
            f.write("数据验证结果:\n")
            f.write("-" * 20 + "\n")
            if validation_results['errors']:
                f.write("错误:\n")
                for error in validation_results['errors']:
                    f.write(f"  - {error}\n")
            else:
                f.write("✓ 未发现数据错误\n")

            if validation_results['warnings']:
                f.write("\n警告:\n")
                for warning in validation_results['warnings']:
                    f.write(f"  - {warning}\n")
            else:
                f.write("✓ 未发现数据警告\n")

            # 数据统计
            f.write("\n\n数据统计:\n")
            f.write("-" * 20 + "\n")
            f.write(f"河长范围: {df_long['河长_km'].min():.1f} - {df_long['河长_km'].max():.1f} km\n")
            f.write(f"百分比范围: {df_long['百分比_%'].min():.1f}% - {df_long['百分比_%'].max():.1f}%\n")
            f.write(f"总流量范围: {df_long['总流量_亿立方米'].min():.1f} - {df_long['总流量_亿立方米'].max():.1f} 亿立方米\n")
            f.write(f"废水排放范围: {df_long['废水排放_亿吨'].min():.1f} - {df_long['废水排放_亿吨'].max():.1f} 亿吨\n")

        return report_path


def main():
    """主函数：演示数据处理流程"""
    print("长江流域水质数据处理器 (1995-2004年)")
    print("=" * 50)

    # 创建处理器实例
    processor = YangtzeWaterQualityProcessor()

    # 定义原始数据
    print("1. 加载原始数据...")
    processor.define_raw_data()
    print(f"   ✓ 已加载 {len(processor.raw_data)} 年的数据")

    # 验证数据
    print("\n2. 验证数据质量...")
    validation_results = processor.validate_data()
    if validation_results['errors']:
        print(f"   ⚠ 发现 {len(validation_results['errors'])} 个错误")
        for error in validation_results['errors'][:3]:  # 只显示前3个
            print(f"     - {error}")
    else:
        print("   ✓ 数据验证通过")

    if validation_results['warnings']:
        print(f"   ⚠ 发现 {len(validation_results['warnings'])} 个警告")
        for warning in validation_results['warnings'][:3]:  # 只显示前3个
            print(f"     - {warning}")

    # 处理数据
    print("\n3. 处理数据...")
    df_long = processor.process_to_long_format()
    df_wide = processor.process_to_wide_format()
    df_summary = processor.generate_summary_statistics(df_long)

    print(f"   ✓ 长表格式: {len(df_long)} 条记录")
    print(f"   ✓ 宽表格式: {len(df_wide)} 条记录")
    print(f"   ✓ 汇总统计: {len(df_summary)} 条记录")

    # 导出文件
    print("\n4. 导出CSV文件...")
    files = processor.export_to_csv()
    for file_type, file_path in files.items():
        print(f"   ✓ {file_type}: {file_path}")

    # 生成质量报告
    print("\n5. 生成数据质量报告...")
    report_path = processor.generate_data_quality_report()
    print(f"   ✓ 质量报告: {report_path}")

    # 显示数据预览
    print("\n6. 数据预览:")
    print("-" * 30)
    print("长表格式前5行:")
    print(df_long.head())

    print("\n汇总统计前5行:")
    print(df_summary.head())

    print("\n处理完成！")
    print("=" * 50)

    return processor, df_long, df_wide, df_summary


if __name__ == "__main__":
    # 运行主函数
    processor, df_long, df_wide, df_summary = main()

    # 可选：显示一些基础分析
    print("\n基础分析:")
    print("-" * 20)

    # 水质优良率趋势
    print("年度水质优良率趋势:")
    for _, row in df_summary.iterrows():
        print(f"  {row['年份']}: {row['水质优良率_%']:.1f}%")

    # 废水排放趋势
    print("\n年度废水排放趋势:")
    for _, row in df_summary.iterrows():
        print(f"  {row['年份']}: {row['废水排放_亿吨']:.1f}亿吨")
