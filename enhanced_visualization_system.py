#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强可视化系统
为无matplotlib环境提供文本图表和基础可视化

功能特点：
1. 文本ASCII图表
2. 数据表格美化
3. 趋势分析可视化
4. 风险评估图表
5. 时间戳管理

作者：数学建模团队
日期：2025年
版本：1.0
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os

class EnhancedVisualizationSystem:
    """增强可视化系统"""
    
    def __init__(self, output_dir="enhanced_output"):
        """初始化可视化系统"""
        self.output_dir = output_dir
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(f"{output_dir}/charts", exist_ok=True)
        os.makedirs(f"{output_dir}/reports", exist_ok=True)
        
        print(f"📊 增强可视化系统初始化完成")
        print(f"📁 输出目录: {output_dir}")
        print(f"🕐 时间戳: {self.timestamp}")
    
    def create_ascii_trend_chart(self, data, title="趋势图", width=60, height=20):
        """创建ASCII趋势图"""
        chart_lines = []
        chart_lines.append("=" * (width + 10))
        chart_lines.append(f"{title:^{width + 10}}")
        chart_lines.append("=" * (width + 10))
        chart_lines.append("")
        
        # 数据归一化
        years = data['years']
        values = data['values']
        labels = data.get('labels', ['数据'])

        # 确保values是列表的列表
        if not isinstance(values[0], (list, np.ndarray)):
            values = [values]

        # 计算所有数据的最小值和最大值
        all_values = []
        for series in values:
            all_values.extend(series)
        min_val = min(all_values)
        max_val = max(all_values)
        
        # 创建图表
        chart = [[' ' for _ in range(width)] for _ in range(height)]
        
        # 绘制数据线
        symbols = ['*', '+', 'o', '#', '@']
        for series_idx, series in enumerate(values):
            symbol = symbols[series_idx % len(symbols)]
            
            for i, (year, value) in enumerate(zip(years, series)):
                x = int((i / (len(years) - 1)) * (width - 1))
                y = int(((value - min_val) / (max_val - min_val)) * (height - 1))
                y = height - 1 - y  # 翻转Y轴
                
                if 0 <= x < width and 0 <= y < height:
                    chart[y][x] = symbol
        
        # 添加Y轴标签
        for i in range(height):
            value = max_val - (i / (height - 1)) * (max_val - min_val)
            chart_lines.append(f"{value:6.1f} |{''.join(chart[i])}")
        
        # 添加X轴
        chart_lines.append("       " + "-" * width)
        
        # 添加X轴标签
        x_labels = "       "
        for i in range(0, len(years), max(1, len(years) // 8)):
            pos = int((i / (len(years) - 1)) * (width - 1))
            x_labels += f"{years[i]}"
            x_labels += " " * (8 - len(str(years[i])))
        chart_lines.append(x_labels)
        
        # 添加图例
        chart_lines.append("")
        chart_lines.append("图例:")
        for i, label in enumerate(labels):
            symbol = symbols[i % len(symbols)]
            chart_lines.append(f"  {symbol} - {label}")
        
        chart_lines.append("")
        chart_lines.append("=" * (width + 10))
        
        return '\n'.join(chart_lines)
    
    def create_comparison_table(self, scenarios, title="情景对比表"):
        """创建对比表格"""
        table_lines = []
        table_lines.append("=" * 80)
        table_lines.append(f"{title:^80}")
        table_lines.append("=" * 80)
        table_lines.append("")
        
        # 表头
        header = f"{'情景':<15} {'2014年水质优良率':<20} {'2014年废水排放':<20} {'风险等级':<15}"
        table_lines.append(header)
        table_lines.append("-" * 80)
        
        # 数据行
        for scenario_name, pred in scenarios.items():
            final_quality = pred['quality_rate'][-1]
            final_waste = pred['waste_discharge'][-1]
            
            # 风险等级判断
            if final_quality >= 70:
                risk_level = "中等"
            elif final_quality >= 60:
                risk_level = "较高"
            elif final_quality >= 50:
                risk_level = "高"
            else:
                risk_level = "极高"
            
            row = f"{scenario_name:<15} {final_quality:>8.1f}%{'':<11} {final_waste:>8.1f}亿吨{'':<10} {risk_level:<15}"
            table_lines.append(row)
        
        table_lines.append("-" * 80)
        table_lines.append("")
        
        return '\n'.join(table_lines)
    
    def create_risk_timeline(self, base_scenario, title="风险时间线"):
        """创建风险时间线"""
        timeline_lines = []
        timeline_lines.append("=" * 70)
        timeline_lines.append(f"{title:^70}")
        timeline_lines.append("=" * 70)
        timeline_lines.append("")
        
        years = base_scenario['years']
        quality_rates = base_scenario['quality_rate']
        waste_discharges = base_scenario['waste_discharge']
        
        timeline_lines.append("年份    水质优良率    废水排放    风险状态")
        timeline_lines.append("-" * 70)
        
        for year, quality, waste in zip(years, quality_rates, waste_discharges):
            # 风险状态判断
            if quality >= 70 and waste < 300:
                risk_icon = "🟢"
                risk_text = "低风险"
            elif quality >= 60 and waste < 350:
                risk_icon = "🟡"
                risk_text = "中风险"
            elif quality >= 50 and waste < 400:
                risk_icon = "🟠"
                risk_text = "高风险"
            else:
                risk_icon = "🔴"
                risk_text = "极高风险"
            
            line = f"{year}    {quality:>6.1f}%      {waste:>6.1f}亿吨   {risk_icon} {risk_text}"
            timeline_lines.append(line)
        
        timeline_lines.append("-" * 70)
        timeline_lines.append("")
        
        # 关键节点提醒
        timeline_lines.append("⚠️  关键风险节点:")
        for i, (year, quality, waste) in enumerate(zip(years, quality_rates, waste_discharges)):
            if quality < 60 and (i == 0 or quality_rates[i-1] >= 60):
                timeline_lines.append(f"   🔴 {year}年: 水质优良率首次低于60%")
            if quality < 50 and (i == 0 or quality_rates[i-1] >= 50):
                timeline_lines.append(f"   🔴 {year}年: 水质优良率首次低于50%")
            if waste > 350 and (i == 0 or waste_discharges[i-1] <= 350):
                timeline_lines.append(f"   🔴 {year}年: 废水排放首次超过350亿吨")
        
        timeline_lines.append("")
        timeline_lines.append("=" * 70)
        
        return '\n'.join(timeline_lines)
    
    def create_model_performance_summary(self, model_scores, title="模型性能总结"):
        """创建模型性能总结"""
        summary_lines = []
        summary_lines.append("=" * 90)
        summary_lines.append(f"{title:^90}")
        summary_lines.append("=" * 90)
        summary_lines.append("")
        
        # 表头
        header = f"{'模型名称':<20} {'水质R²':<10} {'水质RMSE':<12} {'废水R²':<10} {'废水RMSE':<12} {'综合评分':<10}"
        summary_lines.append(header)
        summary_lines.append("-" * 90)
        
        # 按综合评分排序
        sorted_models = sorted(model_scores.items(), key=lambda x: x[1]['avg_r2'], reverse=True)
        
        for i, (model_name, scores) in enumerate(sorted_models):
            rank_icon = "🥇" if i == 0 else "🥈" if i == 1 else "🥉" if i == 2 else "  "
            
            row = (f"{rank_icon} {model_name:<17} "
                   f"{scores['quality_r2']:>6.3f}    "
                   f"{scores['quality_rmse']:>8.2f}    "
                   f"{scores['waste_r2']:>6.3f}    "
                   f"{scores['waste_rmse']:>8.2f}    "
                   f"{scores['avg_r2']:>6.3f}")
            summary_lines.append(row)
        
        summary_lines.append("-" * 90)
        summary_lines.append("")
        
        # 最佳模型突出显示
        best_model = sorted_models[0]
        summary_lines.append(f"🏆 最佳模型: {best_model[0].upper()}")
        summary_lines.append(f"   综合R²得分: {best_model[1]['avg_r2']:.3f}")
        summary_lines.append(f"   水质预测精度: R²={best_model[1]['quality_r2']:.3f}")
        summary_lines.append(f"   废水预测精度: R²={best_model[1]['waste_r2']:.3f}")
        summary_lines.append("")
        summary_lines.append("=" * 90)
        
        return '\n'.join(summary_lines)
    
    def generate_enhanced_report(self, scenarios, model_scores, historical_data):
        """生成增强版分析报告"""
        print(f"\n📝 生成增强版分析报告...")
        
        report_content = []
        
        # 报告头部
        report_content.extend([
            "🌊 长江水质预测分析增强报告",
            "=" * 80,
            f"生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}",
            f"报告时间戳: {self.timestamp}",
            "",
        ])
        
        # 1. 趋势图
        trend_data = {
            'years': list(historical_data['years']) + list(scenarios['基准情景']['years']),
            'values': [
                list(historical_data['quality_rate']) + list(scenarios['基准情景']['quality_rate']),
                list(historical_data['waste_discharge']) + list(scenarios['基准情景']['waste_discharge'])
            ],
            'labels': ['水质优良率(%)', '废水排放(亿吨)']
        }
        
        # 由于数据量级不同，分别创建图表
        quality_trend = {
            'years': trend_data['years'],
            'values': [trend_data['values'][0]],
            'labels': ['水质优良率(%)']
        }
        
        waste_trend = {
            'years': trend_data['years'],
            'values': [trend_data['values'][1]],
            'labels': ['废水排放(亿吨)']
        }
        
        report_content.append(self.create_ascii_trend_chart(quality_trend, "水质优良率历史与预测趋势"))
        report_content.append("")
        report_content.append(self.create_ascii_trend_chart(waste_trend, "废水排放量历史与预测趋势"))
        report_content.append("")
        
        # 2. 情景对比表
        report_content.append(self.create_comparison_table(scenarios))
        report_content.append("")
        
        # 3. 风险时间线
        report_content.append(self.create_risk_timeline(scenarios['基准情景']))
        report_content.append("")
        
        # 4. 模型性能总结
        report_content.append(self.create_model_performance_summary(model_scores))
        report_content.append("")
        
        # 5. 详细分析
        base_scenario = scenarios['基准情景']
        final_quality = base_scenario['quality_rate'][-1]
        final_waste = base_scenario['waste_discharge'][-1]
        
        report_content.extend([
            "📊 详细分析结果",
            "=" * 80,
            "",
            "🔮 核心预测结果 (基准情景):",
            f"   • 2014年水质优良率: {final_quality:.1f}%",
            f"   • 2014年废水排放: {final_waste:.1f}亿吨",
            f"   • 相比2004年水质下降: {68.0 - final_quality:.1f}个百分点",
            f"   • 相比2004年废水增加: {final_waste - 285.0:.1f}亿吨",
            "",
            "⚠️  风险评估:",
            "   • 如不采取措施，水质将持续恶化",
            "   • 2008年后进入高风险状态",
            "   • 2012年后进入极高风险状态",
            "   • 环境承载力将达到临界点",
            "",
            "💡 政策建议:",
            "   • 立即启动污染源治理工程",
            "   • 实施最严格排放标准",
            "   • 建立跨区域协调机制",
            "   • 加强环境监测预警",
            "",
            "🎯 关键结论:",
            "   • 必须在2008年前采取有效措施",
            "   • 水质保护刻不容缓",
            "   • 需要系统性综合治理",
            "",
            "=" * 80,
            f"报告生成完成 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "=" * 80,
        ])
        
        # 保存报告
        report_filename = f"{self.output_dir}/reports/enhanced_report_{self.timestamp}.txt"
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_content))
        
        print(f"✅ 增强版报告生成完成: {report_filename}")
        return report_filename
    
    def save_chart_files(self, scenarios, model_scores, historical_data):
        """保存各种图表文件"""
        print(f"\n💾 保存图表文件...")
        
        chart_files = []
        
        # 1. 趋势图
        quality_trend = {
            'years': list(historical_data['years']) + list(scenarios['基准情景']['years']),
            'values': [list(historical_data['quality_rate']) + list(scenarios['基准情景']['quality_rate'])],
            'labels': ['水质优良率(%)']
        }
        
        trend_chart = self.create_ascii_trend_chart(quality_trend, "水质优良率趋势预测")
        trend_filename = f"{self.output_dir}/charts/quality_trend_{self.timestamp}.txt"
        with open(trend_filename, 'w', encoding='utf-8') as f:
            f.write(trend_chart)
        chart_files.append(trend_filename)
        
        # 2. 对比表
        comparison_table = self.create_comparison_table(scenarios, "情景对比分析")
        comparison_filename = f"{self.output_dir}/charts/scenario_comparison_{self.timestamp}.txt"
        with open(comparison_filename, 'w', encoding='utf-8') as f:
            f.write(comparison_table)
        chart_files.append(comparison_filename)
        
        # 3. 风险时间线
        risk_timeline = self.create_risk_timeline(scenarios['基准情景'], "风险评估时间线")
        risk_filename = f"{self.output_dir}/charts/risk_timeline_{self.timestamp}.txt"
        with open(risk_filename, 'w', encoding='utf-8') as f:
            f.write(risk_timeline)
        chart_files.append(risk_filename)
        
        # 4. 模型性能
        model_summary = self.create_model_performance_summary(model_scores, "模型性能评估")
        model_filename = f"{self.output_dir}/charts/model_performance_{self.timestamp}.txt"
        with open(model_filename, 'w', encoding='utf-8') as f:
            f.write(model_summary)
        chart_files.append(model_filename)
        
        print(f"✅ 图表文件保存完成，共{len(chart_files)}个文件")
        return chart_files


def main():
    """主函数 - 演示增强可视化系统"""
    print("📊 增强可视化系统演示")
    print("=" * 50)
    
    # 模拟数据
    historical_data = {
        'years': np.array([1995, 1996, 1997, 1998, 1999, 2000, 2001, 2002, 2003, 2004]),
        'quality_rate': np.array([91.7, 89.2, 87.5, 85.8, 82.3, 79.1, 76.4, 73.2, 70.6, 68.0]),
        'waste_discharge': np.array([174, 186, 198, 210, 225, 240, 255, 268, 276, 285])
    }
    
    scenarios = {
        '基准情景': {
            'years': np.array([2005, 2006, 2007, 2008, 2009, 2010, 2011, 2012, 2013, 2014]),
            'quality_rate': np.array([65.6, 63.2, 60.9, 58.5, 56.1, 53.7, 51.4, 49.0, 46.6, 44.3]),
            'waste_discharge': np.array([296, 307, 318, 329, 340, 352, 363, 374, 385, 396])
        },
        '乐观情景': {
            'years': np.array([2005, 2006, 2007, 2008, 2009, 2010, 2011, 2012, 2013, 2014]),
            'quality_rate': np.array([66.8, 65.6, 64.5, 63.3, 62.1, 60.9, 59.7, 58.5, 57.3, 56.1]),
            'waste_discharge': np.array([290, 296, 302, 308, 314, 320, 326, 332, 338, 344])
        },
        '悲观情景': {
            'years': np.array([2005, 2006, 2007, 2008, 2009, 2010, 2011, 2012, 2013, 2014]),
            'quality_rate': np.array([64.4, 60.8, 57.3, 53.7, 50.1, 46.6, 43.0, 39.4, 35.9, 32.4]),
            'waste_discharge': np.array([302, 318, 335, 351, 367, 384, 400, 416, 433, 449])
        }
    }
    
    model_scores = {
        'linear': {'quality_r2': 0.756, 'quality_rmse': 2.85, 'waste_r2': 0.891, 'waste_rmse': 8.2, 'avg_r2': 0.824},
        'ridge': {'quality_r2': 0.752, 'quality_rmse': 2.88, 'waste_r2': 0.888, 'waste_rmse': 8.3, 'avg_r2': 0.820},
        'random_forest': {'quality_r2': 0.768, 'quality_rmse': 2.78, 'waste_r2': 0.895, 'waste_rmse': 8.0, 'avg_r2': 0.832}
    }
    
    # 创建可视化系统
    viz_system = EnhancedVisualizationSystem()
    
    # 生成报告和图表
    report_file = viz_system.generate_enhanced_report(scenarios, model_scores, historical_data)
    chart_files = viz_system.save_chart_files(scenarios, model_scores, historical_data)
    
    print(f"\n✅ 演示完成！")
    print(f"📝 报告文件: {report_file}")
    print(f"📊 图表文件: {len(chart_files)}个")


if __name__ == "__main__":
    main()
