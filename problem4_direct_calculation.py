#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
长江水质问题4直接计算方案

问题4：根据预测分析，如果未来10年内每年都要求长江干流的IV类和V类水的比例控制在20%以内，
且没有劣V类水，那么每年需要处理多少污水？

基于现有预测结果的直接计算
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def main():
    print("🌊 长江水质问题4：污水处理量直接计算")
    print("="*80)
    print("问题：如果未来10年内每年都要求长江干流的IV类和V类水的比例")
    print("控制在20%以内，且没有劣V类水，那么每年需要处理多少污水？")
    print("="*80)
    
    # 基于现有预测结果的数据（来自test_chinese_output_20250728_222843的分析）
    # 这里使用基准情景的预测结果
    prediction_data = {
        2005: {'优良率': 65.6, 'IV_V_类': 25.0, '劣V类': 9.4},
        2006: {'优良率': 63.2, 'IV_V_类': 27.3, '劣V类': 9.5},
        2007: {'优良率': 60.8, 'IV_V_类': 29.6, '劣V类': 9.6},
        2008: {'优良率': 58.5, 'IV_V_类': 31.9, '劣V类': 9.6},
        2009: {'优良率': 56.1, 'IV_V_类': 34.2, '劣V类': 9.7},
        2010: {'优良率': 53.7, 'IV_V_类': 36.6, '劣V类': 9.7},
        2011: {'优良率': 51.4, 'IV_V_类': 38.9, '劣V类': 9.7},
        2012: {'优良率': 49.0, 'IV_V_类': 41.2, '劣V类': 9.8},
        2013: {'优良率': 46.6, 'IV_V_类': 43.5, '劣V类': 9.9},
        2014: {'优良率': 44.3, 'IV_V_类': 45.8, '劣V类': 9.9}
    }
    
    # 目标设定
    target_iv_v_ratio = 20.0  # IV类+V类≤20%
    target_inferior_v_ratio = 0.0  # 劣V类=0%
    
    # 水环境参数
    annual_flow = 9000  # 年均流量(亿立方米)
    treatment_efficiency = 0.9  # 处理效率90%
    
    # 污染物浓度参数
    # 基于水质标准的COD浓度差异
    cod_improvement_factor = 8  # 从IV类(10mg/L)改善到III类(6mg/L)的差值为4mg/L
    # 从劣V类(25mg/L)改善到III类(6mg/L)的差值为19mg/L
    # 加权平均改善因子约为8mg/L
    
    # 典型污水COD浓度
    wastewater_cod = 300  # mg/L
    
    print("\n计算方法说明：")
    print("1. 基于现有预测模型的水质分布预测")
    print("2. 计算需要改善的水质比例")
    print("3. 基于污染负荷平衡原理计算处理量")
    print("4. 考虑90%的污水处理效率")
    
    print("\n年度污水处理量计算结果：")
    print("-" * 90)
    print(f"{'年份':<6} {'预测IV+V':<10} {'预测劣V':<10} {'需改善':<10} {'处理量':<12} {'累计处理量':<12}")
    print(f"{'':^6} {'(%)':<10} {'(%)':<10} {'(%)':<10} {'(亿吨)':<12} {'(亿吨)':<12}")
    print("-" * 90)
    
    results = []
    total_treatment = 0
    cumulative_treatment = 0
    
    for year in range(2005, 2015):
        # 获取预测数据
        data = prediction_data[year]
        current_iv_v = data['IV_V_类']
        current_inferior_v = data['劣V类']
        
        # 计算需要改善的比例
        excess_iv_v = max(0, current_iv_v - target_iv_v_ratio)
        excess_inferior_v = current_inferior_v
        total_improvement = excess_iv_v + excess_inferior_v
        
        # 计算污水处理量
        if total_improvement > 0:
            # 基于污染负荷计算
            # 需要削减的污染负荷 = 改善比例 × 流量 × 浓度改善因子
            pollution_reduction = (total_improvement / 100) * annual_flow * cod_improvement_factor * 10  # 万吨
            
            # 需要处理的污水量 = 污染削减量 / (污水浓度 × 处理效率)
            wastewater_volume = pollution_reduction / (wastewater_cod * treatment_efficiency / 100)  # 亿吨
            wastewater_volume = max(0, wastewater_volume)
        else:
            wastewater_volume = 0
        
        cumulative_treatment += wastewater_volume
        total_treatment += wastewater_volume
        
        results.append({
            'year': year,
            'predicted_iv_v': current_iv_v,
            'predicted_inferior_v': current_inferior_v,
            'improvement_needed': total_improvement,
            'wastewater_volume': wastewater_volume,
            'cumulative': cumulative_treatment
        })
        
        print(f"{year:<6} {current_iv_v:<10.1f} {current_inferior_v:<10.1f} "
              f"{total_improvement:<10.1f} {wastewater_volume:<12.1f} {cumulative_treatment:<12.1f}")
    
    print("-" * 90)
    avg_treatment = total_treatment / 10
    print(f"{'总计':<6} {'':<10} {'':<10} {'':<10} {total_treatment:<12.1f} {'':<12}")
    print(f"{'平均':<6} {'':<10} {'':<10} {'':<10} {avg_treatment:<12.1f} {'':<12}")
    
    # 创建可视化图表
    print("\n📊 生成可视化图表...")
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
    
    years = [r['year'] for r in results]
    volumes = [r['wastewater_volume'] for r in results]
    improvements = [r['improvement_needed'] for r in results]
    
    # 图1：年度污水处理量
    bars = ax1.bar(years, volumes, color='steelblue', alpha=0.7, edgecolor='navy', width=0.6)
    ax1.set_title('未来10年每年需要处理的污水量', fontsize=16, fontweight='bold', pad=20)
    ax1.set_xlabel('年份', fontsize=12)
    ax1.set_ylabel('污水处理量 (亿吨)', fontsize=12)
    ax1.grid(True, alpha=0.3, axis='y')
    
    # 添加数值标签
    for bar, volume in zip(bars, volumes):
        height = bar.get_height()
        if height > 0:
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{volume:.1f}', ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    # 图2：水质改善需求趋势
    line = ax2.plot(years, improvements, 'ro-', linewidth=3, markersize=8, label='需改善比例')
    ax2.axhline(y=20, color='red', linestyle='--', linewidth=2, alpha=0.7, label='IV+V类目标线(20%)')
    ax2.axhline(y=0, color='green', linestyle='--', linewidth=2, alpha=0.7, label='劣V类目标线(0%)')
    ax2.set_title('水质改善需求趋势', fontsize=14, fontweight='bold')
    ax2.set_xlabel('年份', fontsize=12)
    ax2.set_ylabel('需改善比例 (%)', fontsize=12)
    ax2.legend(fontsize=10)
    ax2.grid(True, alpha=0.3)
    
    # 添加数值标签
    for year, improvement in zip(years, improvements):
        if improvement > 0:
            ax2.text(year, improvement + 1, f'{improvement:.1f}%', 
                    ha='center', va='bottom', fontsize=9)
    
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    chart_path = f'problem4_wastewater_treatment_calculation_{timestamp}.png'
    plt.savefig(chart_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    # 生成详细报告
    print("\n📝 生成分析报告...")
    
    report_path = f'problem4_detailed_report_{timestamp}.txt'
    
    max_treatment = max(r['wastewater_volume'] for r in results)
    max_year = [r['year'] for r in results if r['wastewater_volume'] == max_treatment][0]
    
    report_content = [
        "="*80,
        "长江水质问题4详细分析报告：污水处理量计算",
        "="*80,
        "",
        "问题描述：",
        "根据预测分析，如果未来10年内每年都要求长江干流的IV类和V类水的比例",
        "控制在20%以内，且没有劣V类水，那么每年需要处理多少污水？",
        "",
        "="*80,
        "1. 计算方法",
        "="*80,
        "",
        "1.1 基础数据",
        "• 基于现有预测模型的未来10年水质分布预测",
        "• 年均流量：9000亿立方米",
        "• 污水处理效率：90%",
        "• 典型污水COD浓度：300mg/L",
        "",
        "1.2 目标设定",
        "• IV类+V类水质比例：≤20%",
        "• 劣V类水质比例：0%（完全消除）",
        "",
        "1.3 计算原理",
        "• 基于污染负荷平衡方程",
        "• 考虑水质标准差异和处理效率",
        "• 采用保守估算确保目标实现",
        "",
        "="*80,
        "2. 计算结果",
        "="*80,
        "",
        "2.1 总体统计",
        f"• 10年总处理量：{total_treatment:.1f}亿吨",
        f"• 年均处理量：{avg_treatment:.1f}亿吨",
        f"• 最大年处理量：{max_treatment:.1f}亿吨（{max_year}年）",
        f"• 处理量范围：{min(r['wastewater_volume'] for r in results):.1f} - {max_treatment:.1f}亿吨",
        "",
        "2.2 年度明细",
        "-" * 70,
        f"{'年份':<6} {'需改善比例':<12} {'处理量':<12} {'累计处理量':<12}",
        f"{'':^6} {'(%)':<12} {'(亿吨)':<12} {'(亿吨)':<12}",
        "-" * 70,
    ]
    
    for result in results:
        report_content.append(
            f"{result['year']:<6} {result['improvement_needed']:<12.1f} "
            f"{result['wastewater_volume']:<12.1f} {result['cumulative']:<12.1f}"
        )
    
    report_content.extend([
        "-" * 70,
        "",
        "="*80,
        "3. 关键发现",
        "="*80,
        "",
        f"• 处理需求呈递增趋势：从2005年的{results[0]['wastewater_volume']:.1f}亿吨增至2014年的{results[-1]['wastewater_volume']:.1f}亿吨",
        f"• 高峰处理年份：{max_year}年需要处理{max_treatment:.1f}亿吨污水",
        f"• 持续治理压力：10年间需要累计处理{total_treatment:.1f}亿吨污水",
        "• 治理难度递增：随着时间推移，需要改善的水质比例不断增加",
        "",
        "="*80,
        "4. 政策建议",
        "="*80,
        "",
        "4.1 基础设施规划",
        f"• 建设年处理能力{avg_treatment:.0f}亿吨的污水处理设施",
        f"• 预留{max_treatment - avg_treatment:.0f}亿吨的峰值处理能力",
        "• 优先建设高效脱氮除磷工艺设施",
        "",
        "4.2 分阶段实施",
        "• 2005-2008年：重点消除劣V类水质",
        "• 2009-2012年：控制IV类和V类水质扩张",
        "• 2013-2014年：巩固治理成果，提升整体水质",
        "",
        "4.3 技术路径",
        "• 采用先进的生物脱氮除磷技术",
        "• 建设人工湿地等生态处理设施",
        "• 实施污水资源化利用",
        "• 推广分散式处理技术",
        "",
        "4.4 保障措施",
        "• 建立稳定的资金投入机制",
        "• 完善污水处理监管体系",
        "• 加强技术研发和人才培养",
        "• 建立区域协调治理机制",
        "",
        "="*80,
        "5. 结论",
        "="*80,
        "",
        f"要实现长江干流IV类和V类水比例控制在20%以内、无劣V类水的目标，",
        f"未来10年需要累计处理{total_treatment:.1f}亿吨污水，年均{avg_treatment:.1f}亿吨。",
        "",
        f"这需要大规模的污水处理基础设施建设投入，建议总投资规模",
        f"按照每吨处理能力1000-1500元计算，约需{avg_treatment*1000:.0f}-{avg_treatment*1500:.0f}亿元。",
        "",
        "建议采用分阶段、分区域的治理策略，优先消除劣V类水质，",
        "逐步控制IV类和V类水质比例，最终实现长江水质的根本改善。",
        "",
        "="*80,
        f"报告生成时间：{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}",
        "="*80,
    ])
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(report_content))
    
    # 最终总结
    print("\n" + "="*80)
    print("🎉 问题4分析完成！")
    print("="*80)
    print(f"✅ 核心结论：")
    print(f"   • 10年总处理量：{total_treatment:.1f}亿吨")
    print(f"   • 年均处理量：{avg_treatment:.1f}亿吨")
    print(f"   • 最大年处理量：{max_treatment:.1f}亿吨（{max_year}年）")
    print(f"")
    print(f"📊 输出文件：")
    print(f"   • 可视化图表：{chart_path}")
    print(f"   • 详细报告：{report_path}")
    print(f"")
    print(f"💡 关键建议：")
    print(f"   • 需要建设年处理能力{avg_treatment:.0f}亿吨的污水处理设施")
    print(f"   • 估算总投资需求：{avg_treatment*1000:.0f}-{avg_treatment*1500:.0f}亿元")
    print(f"   • 建议采用分阶段治理策略")
    print(f"   • 优先消除劣V类水质")

if __name__ == "__main__":
    main()
