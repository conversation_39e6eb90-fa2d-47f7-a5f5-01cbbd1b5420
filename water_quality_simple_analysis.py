#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
长江流域水质数据简化分析
基于1995-2004年水质报告数据（无图表版本）

作者：数学建模团队
日期：2025年
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

class SimpleWaterQualityAnalyzer:
    """长江流域水质数据简化分析器"""
    
    def __init__(self, csv_path_long: str, csv_path_summary: str):
        """
        初始化分析器
        
        Args:
            csv_path_long: 长表格式CSV文件路径
            csv_path_summary: 汇总统计CSV文件路径
        """
        self.df_long = pd.read_csv(csv_path_long, encoding='utf-8-sig')
        self.df_summary = pd.read_csv(csv_path_summary, encoding='utf-8-sig')
        
        # 数据预处理
        self.df_long['年份'] = self.df_long['年份'].astype(int)
        self.df_summary['年份'] = self.df_summary['年份'].astype(int)
    
    def analyze_trends(self):
        """分析趋势"""
        print("长江流域水质数据深度分析报告 (1995-2004)")
        print("=" * 60)
        
        # 1. 基本统计信息
        print("\n1. 基本统计信息:")
        print("-" * 30)
        print(f"   数据年份范围: {self.df_summary['年份'].min()}-{self.df_summary['年份'].max()}")
        print(f"   水质优良率范围: {self.df_summary['水质优良率_%'].min():.1f}% - {self.df_summary['水质优良率_%'].max():.1f}%")
        print(f"   平均水质优良率: {self.df_summary['水质优良率_%'].mean():.1f}%")
        print(f"   废水排放量范围: {self.df_summary['废水排放_亿吨'].min():.1f} - {self.df_summary['废水排放_亿吨'].max():.1f} 亿吨")
        print(f"   总流量范围: {self.df_summary['总流量_亿立方米'].min():.1f} - {self.df_summary['总流量_亿立方米'].max():.1f} 亿立方米")
        
        # 2. 趋势分析
        print("\n2. 趋势分析:")
        print("-" * 30)
        quality_trend = np.polyfit(self.df_summary['年份'], self.df_summary['水质优良率_%'], 1)[0]
        waste_trend = np.polyfit(self.df_summary['年份'], self.df_summary['废水排放_亿吨'], 1)[0]
        flow_trend = np.polyfit(self.df_summary['年份'], self.df_summary['总流量_亿立方米'], 1)[0]
        
        print(f"   水质优良率年均变化: {quality_trend:.2f}%/年 ({'下降' if quality_trend < 0 else '上升'})")
        print(f"   废水排放年均变化: {waste_trend:.2f}亿吨/年 ({'减少' if waste_trend < 0 else '增加'})")
        print(f"   总流量年均变化: {flow_trend:.2f}亿立方米/年 ({'减少' if flow_trend < 0 else '增加'})")
        
        # 3. 相关性分析
        print("\n3. 相关性分析:")
        print("-" * 30)
        corr_quality_waste = np.corrcoef(self.df_summary['废水排放_亿吨'], self.df_summary['水质优良率_%'])[0, 1]
        corr_quality_flow = np.corrcoef(self.df_summary['总流量_亿立方米'], self.df_summary['水质优良率_%'])[0, 1]
        corr_waste_flow = np.corrcoef(self.df_summary['废水排放_亿吨'], self.df_summary['总流量_亿立方米'])[0, 1]
        
        print(f"   水质优良率 vs 废水排放: {corr_quality_waste:.3f} ({'强' if abs(corr_quality_waste) > 0.7 else '中等' if abs(corr_quality_waste) > 0.3 else '弱'}{'负' if corr_quality_waste < 0 else '正'}相关)")
        print(f"   水质优良率 vs 总流量: {corr_quality_flow:.3f} ({'强' if abs(corr_quality_flow) > 0.7 else '中等' if abs(corr_quality_flow) > 0.3 else '弱'}{'负' if corr_quality_flow < 0 else '正'}相关)")
        print(f"   废水排放 vs 总流量: {corr_waste_flow:.3f} ({'强' if abs(corr_waste_flow) > 0.7 else '中等' if abs(corr_waste_flow) > 0.3 else '弱'}{'负' if corr_waste_flow < 0 else '正'}相关)")
        
        # 4. 关键发现
        print("\n4. 关键发现:")
        print("-" * 30)
        worst_year = self.df_summary.loc[self.df_summary['水质优良率_%'].idxmin(), '年份']
        best_year = self.df_summary.loc[self.df_summary['水质优良率_%'].idxmax(), '年份']
        max_waste_year = self.df_summary.loc[self.df_summary['废水排放_亿吨'].idxmax(), '年份']
        max_flow_year = self.df_summary.loc[self.df_summary['总流量_亿立方米'].idxmax(), '年份']
        
        print(f"   水质最好年份: {best_year}年 (优良率: {self.df_summary['水质优良率_%'].max():.1f}%)")
        print(f"   水质最差年份: {worst_year}年 (优良率: {self.df_summary['水质优良率_%'].min():.1f}%)")
        print(f"   废水排放最多年份: {max_waste_year}年 ({self.df_summary['废水排放_亿吨'].max():.1f}亿吨)")
        print(f"   总流量最大年份: {max_flow_year}年 ({self.df_summary['总流量_亿立方米'].max():.1f}亿立方米)")
        
        total_decline = self.df_summary['水质优良率_%'].iloc[-1] - self.df_summary['水质优良率_%'].iloc[0]
        total_waste_increase = self.df_summary['废水排放_亿吨'].iloc[-1] - self.df_summary['废水排放_亿吨'].iloc[0]
        
        print(f"   10年间水质优良率变化: {total_decline:.1f}个百分点")
        print(f"   10年间废水排放增长: {total_waste_increase:.1f}亿吨 ({total_waste_increase/self.df_summary['废水排放_亿吨'].iloc[0]*100:.1f}%)")
    
    def analyze_water_grades(self):
        """分析水质等级分布"""
        print("\n5. 水质等级分布分析:")
        print("-" * 30)
        
        # 计算各年份各水质等级的平均比例
        grade_stats = self.df_long.groupby(['年份', '水质等级'])['百分比_%'].mean().reset_index()
        grade_pivot = grade_stats.pivot(index='年份', columns='水质等级', values='百分比_%').fillna(0)
        
        # 确保列的顺序
        grade_order = ['Ⅰ类', 'Ⅱ类', 'Ⅲ类', 'Ⅳ类', 'Ⅴ类', '劣Ⅴ类']
        grade_pivot = grade_pivot.reindex(columns=grade_order, fill_value=0)
        
        print("   各水质等级年均比例:")
        for grade in grade_order:
            if grade in grade_pivot.columns:
                avg_pct = grade_pivot[grade].mean()
                trend = np.polyfit(grade_pivot.index, grade_pivot[grade], 1)[0]
                print(f"     {grade}: {avg_pct:.1f}% (年均变化: {trend:+.2f}%/年)")
        
        # 优良水质（Ⅰ-Ⅲ类）vs 污染水质（Ⅳ-劣Ⅴ类）
        good_grades = grade_pivot[['Ⅰ类', 'Ⅱ类', 'Ⅲ类']].sum(axis=1)
        bad_grades = grade_pivot[['Ⅳ类', 'Ⅴ类', '劣Ⅴ类']].sum(axis=1)
        
        print(f"\n   优良水质(Ⅰ-Ⅲ类)平均比例: {good_grades.mean():.1f}%")
        print(f"   污染水质(Ⅳ-劣Ⅴ类)平均比例: {bad_grades.mean():.1f}%")
        
        good_trend = np.polyfit(grade_pivot.index, good_grades, 1)[0]
        bad_trend = np.polyfit(grade_pivot.index, bad_grades, 1)[0]
        
        print(f"   优良水质年均变化: {good_trend:+.2f}%/年")
        print(f"   污染水质年均变化: {bad_trend:+.2f}%/年")
    
    def analyze_regions(self):
        """分析区域差异"""
        print("\n6. 区域差异分析:")
        print("-" * 30)
        
        # 计算各区域的水质优良率
        good_grades = ['Ⅰ类', 'Ⅱ类', 'Ⅲ类']
        regional_data = []
        
        for year in sorted(self.df_long['年份'].unique()):
            year_data = self.df_long[self.df_long['年份'] == year]
            
            for region in ['全流域', '干流', '支流']:
                region_data = year_data[year_data['范围'] == region]
                total_length = region_data['河长_km'].sum()
                good_length = region_data[region_data['水质等级'].isin(good_grades)]['河长_km'].sum()
                good_ratio = (good_length / total_length * 100) if total_length > 0 else 0
                
                regional_data.append({
                    '年份': year,
                    '范围': region,
                    '水质优良率_%': good_ratio,
                    '总河长_km': total_length
                })
        
        regional_df = pd.DataFrame(regional_data)
        
        # 各区域统计
        for region in ['全流域', '干流', '支流']:
            region_data = regional_df[regional_df['范围'] == region]
            avg_quality = region_data['水质优良率_%'].mean()
            quality_trend = np.polyfit(region_data['年份'], region_data['水质优良率_%'], 1)[0]
            avg_length = region_data['总河长_km'].mean()
            
            print(f"   {region}:")
            print(f"     平均水质优良率: {avg_quality:.1f}%")
            print(f"     年均变化趋势: {quality_trend:+.2f}%/年")
            print(f"     平均评价河长: {avg_length:.0f}km")
    
    def analyze_seasonal_patterns(self):
        """分析季节性模式"""
        print("\n7. 季节性模式分析:")
        print("-" * 30)
        
        # 按时段分析
        seasonal_stats = self.df_long.groupby(['时段', '水质等级'])['百分比_%'].mean().reset_index()
        seasonal_pivot = seasonal_stats.pivot(index='时段', columns='水质等级', values='百分比_%').fillna(0)
        
        # 计算各时段的优良水质比例
        good_grades = ['Ⅰ类', 'Ⅱ类', 'Ⅲ类']
        for period in ['枯水期', '丰水期', '水文年']:
            if period in seasonal_pivot.index:
                period_data = self.df_long[self.df_long['时段'] == period]
                
                # 计算优良水质比例
                total_length = period_data['河长_km'].sum()
                good_length = period_data[period_data['水质等级'].isin(good_grades)]['河长_km'].sum()
                good_ratio = (good_length / total_length * 100) if total_length > 0 else 0
                
                print(f"   {period}平均水质优良率: {good_ratio:.1f}%")
        
        # 分析枯水期vs丰水期差异
        dry_season = self.df_long[self.df_long['时段'] == '枯水期']
        wet_season = self.df_long[self.df_long['时段'] == '丰水期']
        
        dry_good = dry_season[dry_season['水质等级'].isin(good_grades)]['河长_km'].sum() / dry_season['河长_km'].sum() * 100
        wet_good = wet_season[wet_season['水质等级'].isin(good_grades)]['河长_km'].sum() / wet_season['河长_km'].sum() * 100
        
        print(f"\n   季节性差异:")
        print(f"     枯水期 vs 丰水期优良率差异: {dry_good - wet_good:+.1f}个百分点")
        print(f"     {'枯水期' if dry_good > wet_good else '丰水期'}水质相对更好")
    
    def generate_conclusions(self):
        """生成结论和建议"""
        print("\n8. 主要结论和建议:")
        print("-" * 30)
        
        # 基于分析结果的结论
        quality_decline = self.df_summary['水质优良率_%'].iloc[-1] - self.df_summary['水质优良率_%'].iloc[0]
        waste_increase = self.df_summary['废水排放_亿吨'].iloc[-1] - self.df_summary['废水排放_亿吨'].iloc[0]
        corr_quality_waste = np.corrcoef(self.df_summary['废水排放_亿吨'], self.df_summary['水质优良率_%'])[0, 1]
        
        print("   主要结论:")
        print(f"   1. 1995-2004年间，长江流域水质优良率总体呈下降趋势，下降了{abs(quality_decline):.1f}个百分点")
        print(f"   2. 同期废水排放量大幅增加{waste_increase:.1f}亿吨，增长{waste_increase/self.df_summary['废水排放_亿吨'].iloc[0]*100:.1f}%")
        print(f"   3. 水质优良率与废水排放量呈{'强' if abs(corr_quality_waste) > 0.7 else '中等'}负相关关系(r={corr_quality_waste:.3f})")
        print("   4. 干流水质相对较好，但支流污染问题更为严重")
        print("   5. 水质恶化趋势在2000年后更加明显")
        
        print("\n   政策建议:")
        print("   1. 加强工业废水排放监管，严格执行排放标准")
        print("   2. 重点治理支流污染，实施流域综合治理")
        print("   3. 建立长效监测机制，及时预警水质变化")
        print("   4. 推进清洁生产技术，减少污染物产生")
        print("   5. 加强跨区域协调，统筹流域治理工作")


def main():
    """主函数"""
    print("开始长江流域水质数据深度分析...")
    
    try:
        # 创建分析器
        analyzer = SimpleWaterQualityAnalyzer(
            'yangtze_water_quality_1995_2004_long.csv',
            'yangtze_water_quality_1995_2004_summary.csv'
        )
        
        # 执行各项分析
        analyzer.analyze_trends()
        analyzer.analyze_water_grades()
        analyzer.analyze_regions()
        analyzer.analyze_seasonal_patterns()
        analyzer.generate_conclusions()
        
        print("\n" + "=" * 60)
        print("分析完成！详细报告已生成。")
        
    except FileNotFoundError as e:
        print(f"错误：找不到数据文件 - {e}")
        print("请确保已运行 water_quality_data_processor.py 生成CSV文件")
    except Exception as e:
        print(f"分析过程中出现错误：{e}")


if __name__ == "__main__":
    main()
