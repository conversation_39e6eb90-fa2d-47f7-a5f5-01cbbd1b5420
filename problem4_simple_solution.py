#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
长江水质问题4简化解决方案：污水处理量计算

问题4：根据预测分析，如果未来10年内每年都要求长江干流的IV类和V类水的比例控制在20%以内，
且没有劣V类水，那么每年需要处理多少污水？

作者：水质分析系统
日期：2025-01-29
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def load_and_analyze_data():
    """加载和分析历史数据"""
    print("🌊 长江水质问题4分析：污水处理量计算")
    print("="*80)
    print("问题：如果未来10年内每年都要求长江干流的IV类和V类水的比例")
    print("控制在20%以内，且没有劣V类水，那么每年需要处理多少污水？")
    print("="*80)
    
    # 加载数据
    try:
        data = pd.read_csv('yangtze_water_quality_1995_2004_long.csv')
        print("✅ 成功加载历史水质数据")
    except:
        print("❌ 数据加载失败")
        return None
    
    # 筛选干流枯水期数据作为代表
    mainstream_data = data[(data['范围'] == '干流') & (data['时段'] == '枯水期')].copy()
    print(f"📊 干流数据记录数：{len(mainstream_data)}")
    
    # 按年份汇总水质等级分布
    yearly_stats = mainstream_data.groupby(['年份', '水质等级'])['百分比_%'].mean().reset_index()
    yearly_pivot = yearly_stats.pivot(index='年份', columns='水质等级', values='百分比_%').fillna(0)
    
    # 确保所有水质等级都存在
    grade_order = ['Ⅰ类', 'Ⅱ类', 'Ⅲ类', 'Ⅳ类', 'Ⅴ类', '劣Ⅴ类']
    for grade in grade_order:
        if grade not in yearly_pivot.columns:
            yearly_pivot[grade] = 0
    
    yearly_pivot = yearly_pivot[grade_order]
    yearly_pivot['IV_V_类'] = yearly_pivot['Ⅳ类'] + yearly_pivot['Ⅴ类']
    yearly_pivot['优良率'] = yearly_pivot['Ⅰ类'] + yearly_pivot['Ⅱ类'] + yearly_pivot['Ⅲ类']
    
    print("\n干流水质等级历史变化：")
    print(yearly_pivot.round(1))
    
    return yearly_pivot

def predict_future_water_quality(yearly_pivot):
    """预测未来水质等级分布"""
    print("\n" + "="*60)
    print("🔮 未来水质等级分布预测")
    print("="*60)
    
    prediction_years = list(range(2005, 2015))
    years = yearly_pivot.index.values
    
    # 简化预测：基于历史平均值和趋势
    predictions = {}
    grade_order = ['Ⅰ类', 'Ⅱ类', 'Ⅲ类', 'Ⅳ类', 'Ⅴ类', '劣Ⅴ类']
    
    for grade in grade_order:
        if len(years) > 1:
            # 线性趋势预测
            slope, intercept = np.polyfit(years, yearly_pivot[grade], 1)
            future_values = []
            for year in prediction_years:
                predicted_value = slope * year + intercept
                predicted_value = max(0, min(100, predicted_value))  # 限制在0-100%
                future_values.append(predicted_value)
            predictions[grade] = future_values
        else:
            # 使用最后一年的值
            last_value = yearly_pivot[grade].iloc[-1]
            predictions[grade] = [last_value] * len(prediction_years)
    
    # 标准化预测结果
    prediction_df = pd.DataFrame(predictions, index=prediction_years)
    for year in prediction_years:
        total = prediction_df.loc[year].sum()
        if total > 0:
            prediction_df.loc[year] = prediction_df.loc[year] / total * 100
    
    prediction_df['IV_V_类'] = prediction_df['Ⅳ类'] + prediction_df['Ⅴ类']
    prediction_df['优良率'] = prediction_df['Ⅰ类'] + prediction_df['Ⅱ类'] + prediction_df['Ⅲ类']
    
    print("\n未来10年水质等级分布预测（%）：")
    print(prediction_df.round(1))
    
    return prediction_df

def calculate_wastewater_treatment(prediction_df):
    """计算污水处理量"""
    print("\n" + "="*60)
    print("💧 污水处理量计算")
    print("="*60)
    
    # 目标设定
    target_iv_v_ratio = 20.0  # IV类+V类≤20%
    target_inferior_v_ratio = 0.0  # 劣V类=0%
    
    # 水环境参数
    annual_flow = 9000  # 年均流量(亿立方米)
    treatment_efficiency = 0.9  # 处理效率90%
    
    # 污染物浓度标准(mg/L)
    water_standards = {
        'Ⅰ类': 2, 'Ⅱ类': 4, 'Ⅲ类': 6, 
        'Ⅳ类': 10, 'Ⅴ类': 15, '劣Ⅴ类': 25
    }
    
    # 典型污水浓度
    wastewater_cod = 300  # mg/L
    
    results = []
    
    print("\n年度污水处理量计算：")
    print("-" * 80)
    print(f"{'年份':<6} {'预测IV+V':<10} {'目标IV+V':<10} {'需改善':<10} {'处理量':<10}")
    print(f"{'':^6} {'(%)':<10} {'(%)':<10} {'(%)':<10} {'(亿吨)':<10}")
    print("-" * 80)
    
    total_treatment = 0
    
    for year in prediction_df.index:
        # 当前预测的水质分布
        current_iv_v = prediction_df.loc[year, 'IV_V_类']
        current_inferior_v = prediction_df.loc[year, '劣Ⅴ类']
        
        # 需要改善的比例
        excess_iv_v = max(0, current_iv_v - target_iv_v_ratio)
        excess_inferior_v = current_inferior_v
        total_improvement = excess_iv_v + excess_inferior_v
        
        if total_improvement > 0:
            # 计算当前污染负荷
            current_cod_conc = 0
            for grade in ['Ⅰ类', 'Ⅱ类', 'Ⅲ类', 'Ⅳ类', 'Ⅴ类', '劣Ⅴ类']:
                weight = prediction_df.loc[year, grade] / 100
                current_cod_conc += water_standards[grade] * weight
            
            # 目标污染负荷（将超标水质改善到III类）
            target_cod_conc = current_cod_conc - (total_improvement / 100) * (
                water_standards['Ⅳ类'] - water_standards['Ⅲ类']
            )
            
            # 需要削减的污染负荷(万吨/年)
            cod_reduction = (current_cod_conc - target_cod_conc) * annual_flow * 10
            
            # 需要处理的污水量(亿吨)
            wastewater_volume = cod_reduction / (wastewater_cod * 10) / treatment_efficiency * 10000
            wastewater_volume = max(0, wastewater_volume)
        else:
            wastewater_volume = 0
        
        results.append({
            'year': year,
            'predicted_iv_v': current_iv_v,
            'target_iv_v': min(current_iv_v, target_iv_v_ratio),
            'improvement_needed': total_improvement,
            'wastewater_volume': wastewater_volume
        })
        
        total_treatment += wastewater_volume
        
        print(f"{year:<6} {current_iv_v:<10.1f} {min(current_iv_v, target_iv_v_ratio):<10.1f} "
              f"{total_improvement:<10.1f} {wastewater_volume:<10.1f}")
    
    print("-" * 80)
    print(f"{'总计':<6} {'':<10} {'':<10} {'':<10} {total_treatment:<10.1f}")
    print(f"{'平均':<6} {'':<10} {'':<10} {'':<10} {total_treatment/len(results):<10.1f}")
    
    return results, total_treatment

def create_visualization(results, prediction_df):
    """创建可视化图表"""
    print("\n" + "="*60)
    print("📊 生成可视化图表")
    print("="*60)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
    
    years = [r['year'] for r in results]
    volumes = [r['wastewater_volume'] for r in results]
    improvements = [r['improvement_needed'] for r in results]
    
    # 图1：污水处理量
    bars = ax1.bar(years, volumes, color='steelblue', alpha=0.7, edgecolor='navy')
    ax1.set_title('未来10年每年需要处理的污水量', fontsize=16, fontweight='bold')
    ax1.set_xlabel('年份', fontsize=12)
    ax1.set_ylabel('污水处理量 (亿吨)', fontsize=12)
    ax1.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, volume in zip(bars, volumes):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{volume:.1f}', ha='center', va='bottom', fontsize=10)
    
    # 图2：水质改善需求
    ax2.plot(years, improvements, 'ro-', linewidth=2, markersize=6, label='需改善比例')
    ax2.axhline(y=20, color='red', linestyle='--', alpha=0.7, label='IV+V类目标线(20%)')
    ax2.set_title('需要改善的水质比例', fontsize=14, fontweight='bold')
    ax2.set_xlabel('年份', fontsize=12)
    ax2.set_ylabel('需改善比例 (%)', fontsize=12)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    chart_path = f'problem4_wastewater_treatment_{timestamp}.png'
    plt.savefig(chart_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ 图表已保存：{chart_path}")
    return chart_path

def generate_report(results, total_treatment):
    """生成分析报告"""
    print("\n" + "="*60)
    print("📝 生成分析报告")
    print("="*60)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_path = f'problem4_analysis_report_{timestamp}.txt'
    
    avg_treatment = total_treatment / len(results)
    max_treatment = max(r['wastewater_volume'] for r in results)
    max_year = [r['year'] for r in results if r['wastewater_volume'] == max_treatment][0]
    
    report_content = [
        "="*80,
        "长江水质问题4分析报告：污水处理量计算",
        "="*80,
        "",
        "问题描述：",
        "根据预测分析，如果未来10年内每年都要求长江干流的IV类和V类水的比例",
        "控制在20%以内，且没有劣V类水，那么每年需要处理多少污水？",
        "",
        "="*80,
        "核心结论",
        "="*80,
        "",
        f"• 10年总处理量：{total_treatment:.1f}亿吨",
        f"• 年均处理量：{avg_treatment:.1f}亿吨",
        f"• 最大年处理量：{max_treatment:.1f}亿吨（{max_year}年）",
        f"• 处理量范围：{min(r['wastewater_volume'] for r in results):.1f} - {max_treatment:.1f}亿吨",
        "",
        "="*80,
        "年度处理量明细",
        "="*80,
        "",
        f"{'年份':<6} {'需改善比例':<12} {'处理量':<10} {'累计处理量':<12}",
        f"{'':^6} {'(%)':<12} {'(亿吨)':<10} {'(亿吨)':<12}",
        "-" * 50,
    ]
    
    cumulative = 0
    for result in results:
        cumulative += result['wastewater_volume']
        report_content.append(
            f"{result['year']:<6} {result['improvement_needed']:<12.1f} "
            f"{result['wastewater_volume']:<10.1f} {cumulative:<12.1f}"
        )
    
    report_content.extend([
        "",
        "="*80,
        "政策建议",
        "="*80,
        "",
        "1. 基础设施建设",
        f"   • 规划建设年处理能力{avg_treatment:.0f}亿吨的污水处理设施",
        f"   • 重点关注{max_year}年等高峰年份的处理能力保障",
        "",
        "2. 分阶段实施",
        "   • 第一阶段：消除劣V类水质",
        "   • 第二阶段：控制IV类和V类水质比例",
        "   • 第三阶段：巩固治理成果",
        "",
        "3. 技术路径",
        "   • 采用高效脱氮除磷技术",
        "   • 建设生态处理设施",
        "   • 实施污水资源化利用",
        "",
        "="*80,
        f"报告生成时间：{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}",
        "="*80,
    ])
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(report_content))
    
    print(f"✅ 分析报告已保存：{report_path}")
    return report_path

def main():
    """主函数"""
    try:
        # 1. 加载和分析数据
        yearly_pivot = load_and_analyze_data()
        if yearly_pivot is None:
            return
        
        # 2. 预测未来水质
        prediction_df = predict_future_water_quality(yearly_pivot)
        
        # 3. 计算污水处理量
        results, total_treatment = calculate_wastewater_treatment(prediction_df)
        
        # 4. 创建可视化
        chart_path = create_visualization(results, prediction_df)
        
        # 5. 生成报告
        report_path = generate_report(results, total_treatment)
        
        # 6. 总结
        print("\n" + "="*80)
        print("🎉 问题4分析完成！")
        print("="*80)
        print(f"✅ 核心结论：未来10年需累计处理{total_treatment:.1f}亿吨污水")
        print(f"✅ 年均处理量：{total_treatment/len(results):.1f}亿吨")
        print(f"📊 可视化图表：{chart_path}")
        print(f"📝 详细报告：{report_path}")
        
    except Exception as e:
        print(f"\n💥 程序运行出错：{e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
