#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
长江水质预测分析系统
基于1995-2004年历史数据预测未来10年水质发展趋势

问题3：假如不采取更有效的治理措施，依照过去10年的主要统计数据，
对长江未来水质污染的发展趋势做出预测分析，比如研究未来10年的情况。

作者：数学建模团队
日期：2025年
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import PolynomialFeatures
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.model_selection import cross_val_score
from scipy.optimize import curve_fit
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class YangtzeWaterQualityPredictor:
    """长江水质预测分析器"""
    
    def __init__(self, data_path: str):
        """
        初始化预测器
        
        Args:
            data_path: 历史数据CSV文件路径
        """
        self.data = pd.read_csv(data_path, encoding='utf-8-sig')
        self.data = self.data.dropna()  # 移除空行
        
        # 数据预处理
        self.years = self.data['年份'].values
        self.quality_rate = self.data['水质优良率_%'].values
        self.waste_discharge = self.data['废水排放_亿吨'].values
        self.total_flow = self.data['总流量_亿立方米'].values
        
        # 模型存储
        self.models = {}
        self.predictions = {}
        self.model_scores = {}
        
        print(f"数据加载完成：{len(self.data)}年历史数据 ({self.years[0]}-{self.years[-1]})")
    
    def analyze_historical_trends(self):
        """分析历史趋势"""
        print("\n" + "="*60)
        print("历史趋势分析")
        print("="*60)
        
        # 计算年均变化率
        quality_change_rate = (self.quality_rate[-1] - self.quality_rate[0]) / len(self.years)
        waste_change_rate = (self.waste_discharge[-1] - self.waste_discharge[0]) / len(self.years)
        flow_change_rate = (self.total_flow[-1] - self.total_flow[0]) / len(self.years)
        
        print(f"水质优良率年均变化: {quality_change_rate:.2f}%/年")
        print(f"废水排放年均变化: {waste_change_rate:.2f}亿吨/年")
        print(f"总流量年均变化: {flow_change_rate:.2f}亿立方米/年")
        
        # 计算相关系数
        corr_quality_waste = np.corrcoef(self.quality_rate, self.waste_discharge)[0, 1]
        corr_quality_flow = np.corrcoef(self.quality_rate, self.total_flow)[0, 1]
        
        print(f"\n相关性分析:")
        print(f"水质优良率 vs 废水排放: {corr_quality_waste:.3f}")
        print(f"水质优良率 vs 总流量: {corr_quality_flow:.3f}")
        
        # 趋势分析
        if quality_change_rate < 0:
            print(f"\n⚠️  警告：水质优良率呈下降趋势，年均下降{abs(quality_change_rate):.2f}%")
        
        if waste_change_rate > 0:
            print(f"⚠️  警告：废水排放呈上升趋势，年均增加{waste_change_rate:.2f}亿吨")
        
        return {
            'quality_change_rate': quality_change_rate,
            'waste_change_rate': waste_change_rate,
            'flow_change_rate': flow_change_rate,
            'corr_quality_waste': corr_quality_waste,
            'corr_quality_flow': corr_quality_flow
        }
    
    def fit_linear_model(self):
        """拟合线性回归模型"""
        print("\n拟合线性回归模型...")
        
        # 准备数据
        X = self.years.reshape(-1, 1)
        
        # 水质优良率线性模型
        model_quality = LinearRegression()
        model_quality.fit(X, self.quality_rate)
        quality_pred = model_quality.predict(X)
        quality_r2 = r2_score(self.quality_rate, quality_pred)
        quality_rmse = np.sqrt(mean_squared_error(self.quality_rate, quality_pred))
        
        # 废水排放线性模型
        model_waste = LinearRegression()
        model_waste.fit(X, self.waste_discharge)
        waste_pred = model_waste.predict(X)
        waste_r2 = r2_score(self.waste_discharge, waste_pred)
        waste_rmse = np.sqrt(mean_squared_error(self.waste_discharge, waste_pred))
        
        # 存储模型
        self.models['linear'] = {
            'quality': model_quality,
            'waste': model_waste,
            'quality_r2': quality_r2,
            'waste_r2': waste_r2,
            'quality_rmse': quality_rmse,
            'waste_rmse': waste_rmse
        }
        
        print(f"水质优良率线性模型 - R²: {quality_r2:.3f}, RMSE: {quality_rmse:.2f}")
        print(f"废水排放线性模型 - R²: {waste_r2:.3f}, RMSE: {waste_rmse:.2f}")
        
        return self.models['linear']
    
    def exponential_func(self, x, a, b, c):
        """指数函数"""
        return a * np.exp(b * x) + c
    
    def fit_exponential_model(self):
        """拟合指数模型"""
        print("\n拟合指数模型...")
        
        try:
            # 水质优良率指数衰减模型
            x_norm = self.years - self.years[0]  # 标准化年份
            
            # 初始参数估计
            quality_popt, _ = curve_fit(
                self.exponential_func, 
                x_norm, 
                self.quality_rate,
                p0=[100, -0.1, 50],
                maxfev=5000
            )
            
            quality_pred = self.exponential_func(x_norm, *quality_popt)
            quality_r2 = r2_score(self.quality_rate, quality_pred)
            quality_rmse = np.sqrt(mean_squared_error(self.quality_rate, quality_pred))
            
            # 废水排放指数增长模型
            waste_popt, _ = curve_fit(
                self.exponential_func,
                x_norm,
                self.waste_discharge,
                p0=[100, 0.1, 100],
                maxfev=5000
            )
            
            waste_pred = self.exponential_func(x_norm, *waste_popt)
            waste_r2 = r2_score(self.waste_discharge, waste_pred)
            waste_rmse = np.sqrt(mean_squared_error(self.waste_discharge, waste_pred))
            
            # 存储模型
            self.models['exponential'] = {
                'quality_params': quality_popt,
                'waste_params': waste_popt,
                'quality_r2': quality_r2,
                'waste_r2': waste_r2,
                'quality_rmse': quality_rmse,
                'waste_rmse': waste_rmse
            }
            
            print(f"水质优良率指数模型 - R²: {quality_r2:.3f}, RMSE: {quality_rmse:.2f}")
            print(f"废水排放指数模型 - R²: {waste_r2:.3f}, RMSE: {waste_rmse:.2f}")
            
        except Exception as e:
            print(f"指数模型拟合失败: {e}")
            self.models['exponential'] = None
        
        return self.models.get('exponential')
    
    def fit_polynomial_model(self, degree=2):
        """拟合多项式模型"""
        print(f"\n拟合{degree}次多项式模型...")
        
        # 准备数据
        X = self.years.reshape(-1, 1)
        poly_features = PolynomialFeatures(degree=degree)
        X_poly = poly_features.fit_transform(X)
        
        # 水质优良率多项式模型
        model_quality = LinearRegression()
        model_quality.fit(X_poly, self.quality_rate)
        quality_pred = model_quality.predict(X_poly)
        quality_r2 = r2_score(self.quality_rate, quality_pred)
        quality_rmse = np.sqrt(mean_squared_error(self.quality_rate, quality_pred))
        
        # 废水排放多项式模型
        model_waste = LinearRegression()
        model_waste.fit(X_poly, self.waste_discharge)
        waste_pred = model_waste.predict(X_poly)
        waste_r2 = r2_score(self.waste_discharge, waste_pred)
        waste_rmse = np.sqrt(mean_squared_error(self.waste_discharge, waste_pred))
        
        # 存储模型
        self.models['polynomial'] = {
            'quality': model_quality,
            'waste': model_waste,
            'poly_features': poly_features,
            'degree': degree,
            'quality_r2': quality_r2,
            'waste_r2': waste_r2,
            'quality_rmse': quality_rmse,
            'waste_rmse': waste_rmse
        }
        
        print(f"水质优良率多项式模型 - R²: {quality_r2:.3f}, RMSE: {quality_rmse:.2f}")
        print(f"废水排放多项式模型 - R²: {waste_r2:.3f}, RMSE: {waste_rmse:.2f}")
        
        return self.models['polynomial']
    
    def evaluate_models(self):
        """评估模型性能"""
        print("\n" + "="*60)
        print("模型性能评估")
        print("="*60)
        
        model_comparison = []
        
        for model_name, model_data in self.models.items():
            if model_data is not None:
                quality_r2 = model_data.get('quality_r2', 0)
                waste_r2 = model_data.get('waste_r2', 0)
                quality_rmse = model_data.get('quality_rmse', float('inf'))
                waste_rmse = model_data.get('waste_rmse', float('inf'))
                
                # 综合评分（R²越高越好，RMSE越低越好）
                quality_score = quality_r2 - quality_rmse / 100
                waste_score = waste_r2 - waste_rmse / 100
                total_score = (quality_score + waste_score) / 2
                
                model_comparison.append({
                    'model': model_name,
                    'quality_r2': quality_r2,
                    'waste_r2': waste_r2,
                    'quality_rmse': quality_rmse,
                    'waste_rmse': waste_rmse,
                    'total_score': total_score
                })
        
        # 排序并显示结果
        model_comparison.sort(key=lambda x: x['total_score'], reverse=True)
        
        print("模型性能排名:")
        print("-" * 80)
        print(f"{'模型':<12} {'水质R²':<8} {'废水R²':<8} {'水质RMSE':<10} {'废水RMSE':<10} {'综合评分':<8}")
        print("-" * 80)
        
        for model in model_comparison:
            print(f"{model['model']:<12} {model['quality_r2']:<8.3f} {model['waste_r2']:<8.3f} "
                  f"{model['quality_rmse']:<10.2f} {model['waste_rmse']:<10.2f} {model['total_score']:<8.3f}")
        
        # 选择最佳模型
        best_model = model_comparison[0]['model']
        print(f"\n🏆 最佳模型: {best_model}")
        
        return model_comparison, best_model

    def predict_future(self, future_years, model_name='best'):
        """
        预测未来水质发展趋势

        Args:
            future_years: 预测年份列表
            model_name: 使用的模型名称，'best'表示自动选择最佳模型
        """
        print(f"\n预测未来{len(future_years)}年水质发展趋势...")

        if model_name == 'best':
            _, model_name = self.evaluate_models()

        if model_name not in self.models or self.models[model_name] is None:
            raise ValueError(f"模型 {model_name} 不存在或拟合失败")

        future_years = np.array(future_years)
        model = self.models[model_name]

        # 预测结果
        predictions = {
            'years': future_years,
            'quality_rate': [],
            'waste_discharge': [],
            'model_used': model_name
        }

        if model_name == 'linear':
            # 线性模型预测
            X_future = future_years.reshape(-1, 1)
            quality_pred = model['quality'].predict(X_future)
            waste_pred = model['waste'].predict(X_future)

            predictions['quality_rate'] = quality_pred
            predictions['waste_discharge'] = waste_pred

        elif model_name == 'exponential':
            # 指数模型预测
            x_norm = future_years - self.years[0]
            quality_pred = self.exponential_func(x_norm, *model['quality_params'])
            waste_pred = self.exponential_func(x_norm, *model['waste_params'])

            predictions['quality_rate'] = quality_pred
            predictions['waste_discharge'] = waste_pred

        elif model_name == 'polynomial':
            # 多项式模型预测
            X_future = future_years.reshape(-1, 1)
            X_future_poly = model['poly_features'].transform(X_future)
            quality_pred = model['quality'].predict(X_future_poly)
            waste_pred = model['waste'].predict(X_future_poly)

            predictions['quality_rate'] = quality_pred
            predictions['waste_discharge'] = waste_pred

        # 确保预测值在合理范围内
        predictions['quality_rate'] = np.clip(predictions['quality_rate'], 0, 100)
        predictions['waste_discharge'] = np.maximum(predictions['waste_discharge'], 0)

        self.predictions[model_name] = predictions

        return predictions

    def generate_scenarios(self, future_years):
        """生成不同情景下的预测"""
        print("\n" + "="*60)
        print("情景分析")
        print("="*60)

        scenarios = {}

        # 基准情景：使用最佳模型
        base_pred = self.predict_future(future_years, 'best')
        scenarios['基准情景'] = base_pred

        # 乐观情景：假设治理效果显现，恶化速度减缓50%
        optimistic_quality = []
        optimistic_waste = []

        for i, year in enumerate(future_years):
            # 水质优良率下降速度减缓
            if i == 0:
                quality = self.quality_rate[-1] + (base_pred['quality_rate'][i] - self.quality_rate[-1]) * 0.5
            else:
                quality = optimistic_quality[-1] + (base_pred['quality_rate'][i] - base_pred['quality_rate'][i-1]) * 0.5

            # 废水排放增长速度减缓
            if i == 0:
                waste = self.waste_discharge[-1] + (base_pred['waste_discharge'][i] - self.waste_discharge[-1]) * 0.5
            else:
                waste = optimistic_waste[-1] + (base_pred['waste_discharge'][i] - base_pred['waste_discharge'][i-1]) * 0.5

            optimistic_quality.append(max(quality, 0))
            optimistic_waste.append(max(waste, 0))

        scenarios['乐观情景'] = {
            'years': future_years,
            'quality_rate': np.array(optimistic_quality),
            'waste_discharge': np.array(optimistic_waste),
            'model_used': '乐观调整'
        }

        # 悲观情景：假设污染加速恶化，恶化速度增加50%
        pessimistic_quality = []
        pessimistic_waste = []

        for i, year in enumerate(future_years):
            # 水质优良率下降速度加快
            if i == 0:
                quality = self.quality_rate[-1] + (base_pred['quality_rate'][i] - self.quality_rate[-1]) * 1.5
            else:
                quality = pessimistic_quality[-1] + (base_pred['quality_rate'][i] - base_pred['quality_rate'][i-1]) * 1.5

            # 废水排放增长速度加快
            if i == 0:
                waste = self.waste_discharge[-1] + (base_pred['waste_discharge'][i] - self.waste_discharge[-1]) * 1.5
            else:
                waste = pessimistic_waste[-1] + (base_pred['waste_discharge'][i] - base_pred['waste_discharge'][i-1]) * 1.5

            pessimistic_quality.append(max(quality, 0))
            pessimistic_waste.append(max(waste, 0))

        scenarios['悲观情景'] = {
            'years': future_years,
            'quality_rate': np.array(pessimistic_quality),
            'waste_discharge': np.array(pessimistic_waste),
            'model_used': '悲观调整'
        }

        return scenarios

    def analyze_predictions(self, scenarios):
        """分析预测结果"""
        print("\n预测结果分析:")
        print("-" * 50)

        for scenario_name, pred in scenarios.items():
            print(f"\n{scenario_name} ({pred['model_used']}):")

            # 计算关键指标
            final_quality = pred['quality_rate'][-1]
            final_waste = pred['waste_discharge'][-1]
            quality_decline = pred['quality_rate'][-1] - self.quality_rate[-1]
            waste_increase = pred['waste_discharge'][-1] - self.waste_discharge[-1]

            print(f"  2014年水质优良率: {final_quality:.1f}%")
            print(f"  2014年废水排放: {final_waste:.1f}亿吨")
            print(f"  10年水质变化: {quality_decline:+.1f}个百分点")
            print(f"  10年废水增长: {waste_increase:+.1f}亿吨 ({waste_increase/self.waste_discharge[-1]*100:+.1f}%)")

            # 风险评估
            if final_quality < 50:
                print("  ⚠️  严重警告：水质优良率将低于50%")
            elif final_quality < 60:
                print("  ⚠️  警告：水质优良率将低于60%")

            if final_waste > 400:
                print("  ⚠️  严重警告：废水排放将超过400亿吨")
            elif final_waste > 350:
                print("  ⚠️  警告：废水排放将超过350亿吨")

    def plot_predictions(self, scenarios, save_path=None):
        """绘制预测结果图表"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        # 颜色设置
        colors = {'基准情景': '#1f77b4', '乐观情景': '#2ca02c', '悲观情景': '#d62728'}

        # 1. 水质优良率预测
        ax1.plot(self.years, self.quality_rate, 'ko-', label='历史数据', linewidth=2, markersize=6)

        for scenario_name, pred in scenarios.items():
            ax1.plot(pred['years'], pred['quality_rate'],
                    color=colors[scenario_name], linestyle='--', linewidth=2,
                    marker='s', markersize=5, label=f'{scenario_name}预测')

        ax1.set_title('长江水质优良率预测 (1995-2014)', fontsize=14, fontweight='bold')
        ax1.set_xlabel('年份', fontsize=12)
        ax1.set_ylabel('水质优良率 (%)', fontsize=12)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.set_ylim(0, 100)

        # 2. 废水排放预测
        ax2.plot(self.years, self.waste_discharge, 'ko-', label='历史数据', linewidth=2, markersize=6)

        for scenario_name, pred in scenarios.items():
            ax2.plot(pred['years'], pred['waste_discharge'],
                    color=colors[scenario_name], linestyle='--', linewidth=2,
                    marker='s', markersize=5, label=f'{scenario_name}预测')

        ax2.set_title('长江废水排放量预测 (1995-2014)', fontsize=14, fontweight='bold')
        ax2.set_xlabel('年份', fontsize=12)
        ax2.set_ylabel('废水排放量 (亿吨)', fontsize=12)
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 3. 水质与废水关系
        ax3.scatter(self.waste_discharge, self.quality_rate, c='black', s=80, alpha=0.7, label='历史数据')

        for scenario_name, pred in scenarios.items():
            ax3.scatter(pred['waste_discharge'], pred['quality_rate'],
                       c=colors[scenario_name], s=60, alpha=0.7, label=f'{scenario_name}预测')

        ax3.set_title('水质优良率 vs 废水排放量', fontsize=14, fontweight='bold')
        ax3.set_xlabel('废水排放量 (亿吨)', fontsize=12)
        ax3.set_ylabel('水质优良率 (%)', fontsize=12)
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 4. 预测对比
        scenario_names = list(scenarios.keys())
        final_qualities = [scenarios[name]['quality_rate'][-1] for name in scenario_names]
        final_wastes = [scenarios[name]['waste_discharge'][-1] for name in scenario_names]

        x = np.arange(len(scenario_names))
        width = 0.35

        ax4_twin = ax4.twinx()
        bars1 = ax4.bar(x - width/2, final_qualities, width, label='水质优良率 (%)',
                       color=[colors[name] for name in scenario_names], alpha=0.7)
        bars2 = ax4_twin.bar(x + width/2, final_wastes, width, label='废水排放 (亿吨)',
                            color=[colors[name] for name in scenario_names], alpha=0.5)

        ax4.set_title('2014年预测结果对比', fontsize=14, fontweight='bold')
        ax4.set_xlabel('情景', fontsize=12)
        ax4.set_ylabel('水质优良率 (%)', fontsize=12)
        ax4_twin.set_ylabel('废水排放量 (亿吨)', fontsize=12)
        ax4.set_xticks(x)
        ax4.set_xticklabels(scenario_names)

        # 添加数值标签
        for i, (bar1, bar2) in enumerate(zip(bars1, bars2)):
            ax4.text(bar1.get_x() + bar1.get_width()/2, bar1.get_height() + 1,
                    f'{final_qualities[i]:.1f}%', ha='center', va='bottom', fontsize=10)
            ax4_twin.text(bar2.get_x() + bar2.get_width()/2, bar2.get_height() + 5,
                         f'{final_wastes[i]:.0f}', ha='center', va='bottom', fontsize=10)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

    def generate_report(self, scenarios):
        """生成预测分析报告"""
        print("\n" + "="*80)
        print("长江水质预测分析报告")
        print("="*80)

        print("\n📊 基于1995-2004年历史数据的未来10年预测分析")
        print("假设条件：不采取更有效的治理措施，延续当前发展趋势")

        # 历史趋势总结
        print(f"\n📈 历史趋势总结 (1995-2004):")
        print(f"   • 水质优良率：从{self.quality_rate[0]:.1f}%下降到{self.quality_rate[-1]:.1f}%")
        print(f"   • 废水排放：从{self.waste_discharge[0]:.1f}亿吨增加到{self.waste_discharge[-1]:.1f}亿吨")
        print(f"   • 年均水质下降：{(self.quality_rate[-1] - self.quality_rate[0])/len(self.years):.2f}%/年")
        print(f"   • 年均废水增长：{(self.waste_discharge[-1] - self.waste_discharge[0])/len(self.years):.2f}亿吨/年")

        # 预测结果总结
        print(f"\n🔮 预测结果总结 (2005-2014):")
        for scenario_name, pred in scenarios.items():
            final_quality = pred['quality_rate'][-1]
            final_waste = pred['waste_discharge'][-1]
            quality_change = final_quality - self.quality_rate[-1]
            waste_change = final_waste - self.waste_discharge[-1]

            print(f"\n   {scenario_name}:")
            print(f"     2014年水质优良率: {final_quality:.1f}% ({quality_change:+.1f}个百分点)")
            print(f"     2014年废水排放: {final_waste:.1f}亿吨 ({waste_change:+.1f}亿吨)")

            # 计算年均变化率
            annual_quality_change = quality_change / 10
            annual_waste_change = waste_change / 10
            print(f"     年均水质变化: {annual_quality_change:+.2f}%/年")
            print(f"     年均废水变化: {annual_waste_change:+.2f}亿吨/年")

        # 风险评估
        print(f"\n⚠️  风险评估:")
        base_scenario = scenarios['基准情景']

        if base_scenario['quality_rate'][-1] < 50:
            print("   🔴 极高风险：基准情景下2014年水质优良率将低于50%")
        elif base_scenario['quality_rate'][-1] < 60:
            print("   🟡 高风险：基准情景下2014年水质优良率将低于60%")
        else:
            print("   🟢 中等风险：基准情景下水质仍有改善空间")

        if base_scenario['waste_discharge'][-1] > 400:
            print("   🔴 极高风险：基准情景下2014年废水排放将超过400亿吨")
        elif base_scenario['waste_discharge'][-1] > 350:
            print("   🟡 高风险：基准情景下2014年废水排放将超过350亿吨")

        # 关键转折点分析
        print(f"\n📍 关键转折点分析:")
        for scenario_name, pred in scenarios.items():
            # 找到水质优良率低于60%的年份
            below_60_years = pred['years'][pred['quality_rate'] < 60]
            if len(below_60_years) > 0:
                print(f"   {scenario_name}: {below_60_years[0]}年水质优良率将首次低于60%")

            # 找到废水排放超过350亿吨的年份
            above_350_years = pred['years'][pred['waste_discharge'] > 350]
            if len(above_350_years) > 0:
                print(f"   {scenario_name}: {above_350_years[0]}年废水排放将首次超过350亿吨")

        # 政策建议
        print(f"\n💡 政策建议:")
        print("   1. 立即加强废水处理设施建设，提高处理标准")
        print("   2. 实施更严格的工业排放标准和监管措施")
        print("   3. 推进流域综合治理，重点治理污染严重区域")
        print("   4. 建立长效监测预警机制，及时发现和处理污染问题")
        print("   5. 加大环保投入，发展清洁生产技术")
        print("   6. 强化跨区域协调，统筹流域治理工作")

        # 模型局限性说明
        print(f"\n📝 模型局限性说明:")
        print("   • 预测基于历史趋势延续的假设，未考虑政策变化影响")
        print("   • 未考虑极端气候事件和突发污染事故的影响")
        print("   • 预测精度随时间推移而降低，建议定期更新模型")
        print("   • 模型主要反映宏观趋势，具体数值仅供参考")


def main():
    """主函数"""
    print("长江水质预测分析系统")
    print("="*50)
    print("问题3：假如不采取更有效的治理措施，依照过去10年的主要统计数据，")
    print("对长江未来水质污染的发展趋势做出预测分析，比如研究未来10年的情况。")
    print("="*50)

    try:
        # 1. 初始化预测器
        predictor = YangtzeWaterQualityPredictor('yangtze_water_quality_1995_2004_summary.csv')

        # 2. 分析历史趋势
        trends = predictor.analyze_historical_trends()

        # 3. 拟合多种预测模型
        print("\n" + "="*60)
        print("模型拟合")
        print("="*60)

        predictor.fit_linear_model()
        predictor.fit_exponential_model()
        predictor.fit_polynomial_model(degree=2)
        predictor.fit_polynomial_model(degree=3)

        # 4. 评估模型性能
        model_comparison, best_model = predictor.evaluate_models()

        # 5. 预测未来10年 (2005-2014)
        future_years = list(range(2005, 2015))
        scenarios = predictor.generate_scenarios(future_years)

        # 6. 分析预测结果
        predictor.analyze_predictions(scenarios)

        # 7. 生成可视化图表
        print("\n正在生成预测图表...")
        predictor.plot_predictions(scenarios, 'yangtze_water_quality_predictions.png')

        # 8. 生成详细报告
        predictor.generate_report(scenarios)

        # 9. 保存预测结果
        print("\n正在保存预测结果...")

        # 保存预测数据
        prediction_data = []
        for scenario_name, pred in scenarios.items():
            for i, year in enumerate(pred['years']):
                prediction_data.append({
                    '情景': scenario_name,
                    '年份': year,
                    '水质优良率_%': pred['quality_rate'][i],
                    '废水排放_亿吨': pred['waste_discharge'][i],
                    '模型': pred['model_used']
                })

        prediction_df = pd.DataFrame(prediction_data)
        prediction_df.to_csv('yangtze_water_quality_predictions_2005_2014.csv',
                           index=False, encoding='utf-8-sig')

        # 保存模型评估结果
        model_df = pd.DataFrame(model_comparison)
        model_df.to_csv('model_evaluation_results.csv', index=False, encoding='utf-8-sig')

        print("✅ 预测分析完成！")
        print("生成文件:")
        print("  • yangtze_water_quality_predictions_2005_2014.csv - 预测数据")
        print("  • model_evaluation_results.csv - 模型评估结果")
        print("  • yangtze_water_quality_predictions.png - 预测图表")

    except FileNotFoundError:
        print("❌ 错误：找不到数据文件 'yangtze_water_quality_1995_2004_summary.csv'")
        print("请确保已运行 water_quality_data_processor.py 生成数据文件")
    except Exception as e:
        print(f"❌ 分析过程中出现错误：{e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
