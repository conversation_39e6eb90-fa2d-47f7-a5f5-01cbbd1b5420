#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
长江水质预测分析系统（简化版）
基于1995-2004年历史数据预测未来10年水质发展趋势

问题3：假如不采取更有效的治理措施，依照过去10年的主要统计数据，
对长江未来水质污染的发展趋势做出预测分析，比如研究未来10年的情况。

作者：数学建模团队
日期：2025年
"""

import pandas as pd
import numpy as np
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import PolynomialFeatures
from sklearn.metrics import mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

class SimpleYangtzePredictor:
    """长江水质简化预测器"""
    
    def __init__(self, data_path: str):
        """初始化预测器"""
        self.data = pd.read_csv(data_path, encoding='utf-8-sig')
        self.data = self.data.dropna()
        
        # 提取数据
        self.years = self.data['年份'].values
        self.quality_rate = self.data['水质优良率_%'].values
        self.waste_discharge = self.data['废水排放_亿吨'].values
        
        print(f"数据加载完成：{len(self.data)}年历史数据 ({self.years[0]}-{self.years[-1]})")
    
    def analyze_trends(self):
        """分析历史趋势"""
        print("\n" + "="*60)
        print("历史趋势分析")
        print("="*60)
        
        # 计算变化率
        quality_change = (self.quality_rate[-1] - self.quality_rate[0]) / len(self.years)
        waste_change = (self.waste_discharge[-1] - self.waste_discharge[0]) / len(self.years)
        
        print(f"水质优良率年均变化: {quality_change:.2f}%/年")
        print(f"废水排放年均变化: {waste_change:.2f}亿吨/年")
        
        # 相关性分析
        correlation = np.corrcoef(self.quality_rate, self.waste_discharge)[0, 1]
        print(f"水质优良率与废水排放相关系数: {correlation:.3f}")
        
        return quality_change, waste_change, correlation
    
    def fit_models(self):
        """拟合预测模型"""
        print("\n" + "="*60)
        print("模型拟合")
        print("="*60)
        
        X = self.years.reshape(-1, 1)
        models = {}
        
        # 1. 线性模型
        linear_quality = LinearRegression()
        linear_quality.fit(X, self.quality_rate)
        quality_pred = linear_quality.predict(X)
        quality_r2 = r2_score(self.quality_rate, quality_pred)
        
        linear_waste = LinearRegression()
        linear_waste.fit(X, self.waste_discharge)
        waste_pred = linear_waste.predict(X)
        waste_r2 = r2_score(self.waste_discharge, waste_pred)
        
        models['linear'] = {
            'quality_model': linear_quality,
            'waste_model': linear_waste,
            'quality_r2': quality_r2,
            'waste_r2': waste_r2
        }
        
        print(f"线性模型 - 水质R²: {quality_r2:.3f}, 废水R²: {waste_r2:.3f}")
        
        # 2. 二次多项式模型
        poly_features = PolynomialFeatures(degree=2)
        X_poly = poly_features.fit_transform(X)
        
        poly_quality = LinearRegression()
        poly_quality.fit(X_poly, self.quality_rate)
        quality_pred_poly = poly_quality.predict(X_poly)
        quality_r2_poly = r2_score(self.quality_rate, quality_pred_poly)
        
        poly_waste = LinearRegression()
        poly_waste.fit(X_poly, self.waste_discharge)
        waste_pred_poly = poly_waste.predict(X_poly)
        waste_r2_poly = r2_score(self.waste_discharge, waste_pred_poly)
        
        models['polynomial'] = {
            'quality_model': poly_quality,
            'waste_model': poly_waste,
            'poly_features': poly_features,
            'quality_r2': quality_r2_poly,
            'waste_r2': waste_r2_poly
        }
        
        print(f"多项式模型 - 水质R²: {quality_r2_poly:.3f}, 废水R²: {waste_r2_poly:.3f}")
        
        # 选择最佳模型
        if (quality_r2 + waste_r2) > (quality_r2_poly + waste_r2_poly):
            best_model = 'linear'
        else:
            best_model = 'polynomial'
        
        print(f"最佳模型: {best_model}")
        
        return models, best_model
    
    def predict_future(self, models, best_model, future_years):
        """预测未来趋势"""
        print(f"\n预测未来{len(future_years)}年趋势...")
        
        future_years_array = np.array(future_years)
        X_future = future_years_array.reshape(-1, 1)
        
        model = models[best_model]
        
        if best_model == 'linear':
            quality_pred = model['quality_model'].predict(X_future)
            waste_pred = model['waste_model'].predict(X_future)
        else:  # polynomial
            X_future_poly = model['poly_features'].transform(X_future)
            quality_pred = model['quality_model'].predict(X_future_poly)
            waste_pred = model['waste_model'].predict(X_future_poly)
        
        # 确保预测值在合理范围内
        quality_pred = np.clip(quality_pred, 0, 100)
        waste_pred = np.maximum(waste_pred, 0)
        
        return quality_pred, waste_pred
    
    def generate_scenarios(self, models, best_model, future_years):
        """生成不同情景预测"""
        print("\n" + "="*60)
        print("情景分析")
        print("="*60)
        
        # 基准预测
        base_quality, base_waste = self.predict_future(models, best_model, future_years)
        
        scenarios = {
            '基准情景': {
                'quality': base_quality,
                'waste': base_waste,
                'description': '延续当前趋势'
            }
        }
        
        # 乐观情景：恶化速度减缓50%
        optimistic_quality = []
        optimistic_waste = []
        
        for i, year in enumerate(future_years):
            if i == 0:
                q_change = (base_quality[i] - self.quality_rate[-1]) * 0.5
                w_change = (base_waste[i] - self.waste_discharge[-1]) * 0.5
                optimistic_quality.append(self.quality_rate[-1] + q_change)
                optimistic_waste.append(self.waste_discharge[-1] + w_change)
            else:
                q_change = (base_quality[i] - base_quality[i-1]) * 0.5
                w_change = (base_waste[i] - base_waste[i-1]) * 0.5
                optimistic_quality.append(optimistic_quality[-1] + q_change)
                optimistic_waste.append(optimistic_waste[-1] + w_change)
        
        scenarios['乐观情景'] = {
            'quality': np.array(optimistic_quality),
            'waste': np.array(optimistic_waste),
            'description': '治理效果显现，恶化速度减缓50%'
        }
        
        # 悲观情景：恶化速度加快50%
        pessimistic_quality = []
        pessimistic_waste = []
        
        for i, year in enumerate(future_years):
            if i == 0:
                q_change = (base_quality[i] - self.quality_rate[-1]) * 1.5
                w_change = (base_waste[i] - self.waste_discharge[-1]) * 1.5
                pessimistic_quality.append(self.quality_rate[-1] + q_change)
                pessimistic_waste.append(self.waste_discharge[-1] + w_change)
            else:
                q_change = (base_quality[i] - base_quality[i-1]) * 1.5
                w_change = (base_waste[i] - base_waste[i-1]) * 1.5
                pessimistic_quality.append(pessimistic_quality[-1] + q_change)
                pessimistic_waste.append(pessimistic_waste[-1] + w_change)
        
        scenarios['悲观情景'] = {
            'quality': np.array(pessimistic_quality),
            'waste': np.array(pessimistic_waste),
            'description': '污染加速恶化，恶化速度增加50%'
        }
        
        return scenarios
    
    def analyze_results(self, scenarios, future_years):
        """分析预测结果"""
        print("\n" + "="*60)
        print("预测结果分析")
        print("="*60)
        
        for scenario_name, data in scenarios.items():
            print(f"\n{scenario_name} ({data['description']}):")
            
            final_quality = data['quality'][-1]
            final_waste = data['waste'][-1]
            quality_change = final_quality - self.quality_rate[-1]
            waste_change = final_waste - self.waste_discharge[-1]
            
            print(f"  2014年水质优良率: {final_quality:.1f}%")
            print(f"  2014年废水排放: {final_waste:.1f}亿吨")
            print(f"  10年水质变化: {quality_change:+.1f}个百分点")
            print(f"  10年废水增长: {waste_change:+.1f}亿吨 ({waste_change/self.waste_discharge[-1]*100:+.1f}%)")
            
            # 风险评估
            if final_quality < 50:
                print("  🔴 严重警告：水质优良率将低于50%")
            elif final_quality < 60:
                print("  🟡 警告：水质优良率将低于60%")
            
            if final_waste > 400:
                print("  🔴 严重警告：废水排放将超过400亿吨")
            elif final_waste > 350:
                print("  🟡 警告：废水排放将超过350亿吨")
    
    def generate_detailed_predictions(self, scenarios, future_years):
        """生成详细预测数据"""
        print(f"\n逐年预测结果:")
        print("-" * 80)
        print(f"{'年份':<6} {'基准水质%':<10} {'基准废水':<10} {'乐观水质%':<10} {'乐观废水':<10} {'悲观水质%':<10} {'悲观废水':<10}")
        print("-" * 80)
        
        for i, year in enumerate(future_years):
            base_q = scenarios['基准情景']['quality'][i]
            base_w = scenarios['基准情景']['waste'][i]
            opt_q = scenarios['乐观情景']['quality'][i]
            opt_w = scenarios['乐观情景']['waste'][i]
            pes_q = scenarios['悲观情景']['quality'][i]
            pes_w = scenarios['悲观情景']['waste'][i]
            
            print(f"{year:<6} {base_q:<10.1f} {base_w:<10.1f} {opt_q:<10.1f} {opt_w:<10.1f} {pes_q:<10.1f} {pes_w:<10.1f}")
    
    def save_results(self, scenarios, future_years):
        """保存预测结果"""
        print("\n保存预测结果...")
        
        # 创建预测数据表
        prediction_data = []
        
        for scenario_name, data in scenarios.items():
            for i, year in enumerate(future_years):
                prediction_data.append({
                    '情景': scenario_name,
                    '年份': year,
                    '水质优良率_%': data['quality'][i],
                    '废水排放_亿吨': data['waste'][i],
                    '描述': data['description']
                })
        
        # 保存CSV文件
        df = pd.DataFrame(prediction_data)
        df.to_csv('yangtze_predictions_simple.csv', index=False, encoding='utf-8-sig')
        
        print("✅ 预测结果已保存到 yangtze_predictions_simple.csv")


def main():
    """主函数"""
    print("长江水质预测分析系统（简化版）")
    print("="*60)
    print("问题3：假如不采取更有效的治理措施，依照过去10年的主要统计数据，")
    print("对长江未来水质污染的发展趋势做出预测分析，比如研究未来10年的情况。")
    print("="*60)
    
    try:
        # 1. 初始化预测器
        predictor = SimpleYangtzePredictor('yangtze_water_quality_1995_2004_summary.csv')
        
        # 2. 分析历史趋势
        quality_change, waste_change, correlation = predictor.analyze_trends()
        
        # 3. 拟合预测模型
        models, best_model = predictor.fit_models()
        
        # 4. 预测未来10年 (2005-2014)
        future_years = list(range(2005, 2015))
        scenarios = predictor.generate_scenarios(models, best_model, future_years)
        
        # 5. 分析预测结果
        predictor.analyze_results(scenarios, future_years)
        
        # 6. 生成详细预测
        predictor.generate_detailed_predictions(scenarios, future_years)
        
        # 7. 保存结果
        predictor.save_results(scenarios, future_years)
        
        # 8. 生成总结报告
        print("\n" + "="*80)
        print("总结报告")
        print("="*80)
        
        print(f"\n📊 基于1995-2004年历史数据的预测分析:")
        print(f"   • 历史水质优良率年均下降: {abs(quality_change):.2f}%/年")
        print(f"   • 历史废水排放年均增长: {waste_change:.2f}亿吨/年")
        print(f"   • 水质与废水排放相关系数: {correlation:.3f}")
        
        base_scenario = scenarios['基准情景']
        print(f"\n🔮 基准情景预测结果:")
        print(f"   • 2014年水质优良率: {base_scenario['quality'][-1]:.1f}%")
        print(f"   • 2014年废水排放: {base_scenario['waste'][-1]:.1f}亿吨")
        print(f"   • 10年水质下降: {base_scenario['quality'][-1] - predictor.quality_rate[-1]:.1f}个百分点")
        print(f"   • 10年废水增长: {base_scenario['waste'][-1] - predictor.waste_discharge[-1]:.1f}亿吨")
        
        print(f"\n💡 主要结论:")
        print("   1. 如不采取有效治理措施，长江水质将持续恶化")
        print("   2. 废水排放量将大幅增加，环境压力持续加大")
        print("   3. 水质优良率可能在2010年前后降至60%以下")
        print("   4. 急需加强污染治理和环境保护措施")
        
        print("\n✅ 分析完成！")
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误：{e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
