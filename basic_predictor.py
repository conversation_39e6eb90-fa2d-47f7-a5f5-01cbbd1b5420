#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
长江水质基础预测分析
基于1995-2004年历史数据预测未来10年水质发展趋势

问题3：假如不采取更有效的治理措施，依照过去10年的主要统计数据，
对长江未来水质污染的发展趋势做出预测分析，比如研究未来10年的情况。

作者：数学建模团队
日期：2025年
"""

import pandas as pd

def main():
    """主函数"""
    print("长江水质基础预测分析")
    print("="*60)
    print("问题3：假如不采取更有效的治理措施，依照过去10年的主要统计数据，")
    print("对长江未来水质污染的发展趋势做出预测分析，比如研究未来10年的情况。")
    print("="*60)
    
    try:
        # 读取数据
        data = pd.read_csv('yangtze_water_quality_1995_2004_summary.csv', encoding='utf-8-sig')
        data = data.dropna()
        
        print(f"\n数据加载完成：{len(data)}年历史数据")
        
        # 提取关键数据
        years = data['年份'].tolist()
        quality_rates = data['水质优良率_%'].tolist()
        waste_discharges = data['废水排放_亿吨'].tolist()
        
        print(f"数据范围：{years[0]}-{years[-1]}年")
        
        # 历史趋势分析
        print("\n" + "="*60)
        print("历史趋势分析")
        print("="*60)
        
        # 计算年均变化率
        quality_change_total = quality_rates[-1] - quality_rates[0]
        waste_change_total = waste_discharges[-1] - waste_discharges[0]
        years_span = len(years)
        
        quality_annual_change = quality_change_total / years_span
        waste_annual_change = waste_change_total / years_span
        
        print(f"水质优良率变化：{quality_rates[0]:.1f}% → {quality_rates[-1]:.1f}%")
        print(f"废水排放变化：{waste_discharges[0]:.1f}亿吨 → {waste_discharges[-1]:.1f}亿吨")
        print(f"水质优良率年均变化：{quality_annual_change:.2f}%/年")
        print(f"废水排放年均变化：{waste_annual_change:.2f}亿吨/年")
        
        # 简单线性预测
        print("\n" + "="*60)
        print("线性趋势预测 (2005-2014)")
        print("="*60)
        
        future_years = list(range(2005, 2015))
        predictions = []
        
        print(f"{'年份':<6} {'预测水质优良率%':<15} {'预测废水排放(亿吨)':<18}")
        print("-" * 45)
        
        for i, year in enumerate(future_years):
            years_from_2004 = i + 1
            
            # 线性外推
            predicted_quality = quality_rates[-1] + quality_annual_change * years_from_2004
            predicted_waste = waste_discharges[-1] + waste_annual_change * years_from_2004
            
            # 确保预测值在合理范围内
            predicted_quality = max(0, min(100, predicted_quality))
            predicted_waste = max(0, predicted_waste)
            
            predictions.append({
                'year': year,
                'quality': predicted_quality,
                'waste': predicted_waste
            })
            
            print(f"{year:<6} {predicted_quality:<15.1f} {predicted_waste:<18.1f}")
        
        # 情景分析
        print("\n" + "="*60)
        print("情景分析")
        print("="*60)
        
        # 基准情景（延续当前趋势）
        base_2014_quality = predictions[-1]['quality']
        base_2014_waste = predictions[-1]['waste']
        
        # 乐观情景（恶化速度减缓50%）
        opt_quality_change = quality_annual_change * 0.5
        opt_waste_change = waste_annual_change * 0.5
        opt_2014_quality = quality_rates[-1] + opt_quality_change * 10
        opt_2014_waste = waste_discharges[-1] + opt_waste_change * 10
        opt_2014_quality = max(0, min(100, opt_2014_quality))
        opt_2014_waste = max(0, opt_2014_waste)
        
        # 悲观情景（恶化速度加快50%）
        pes_quality_change = quality_annual_change * 1.5
        pes_waste_change = waste_annual_change * 1.5
        pes_2014_quality = quality_rates[-1] + pes_quality_change * 10
        pes_2014_waste = waste_discharges[-1] + pes_waste_change * 10
        pes_2014_quality = max(0, min(100, pes_2014_quality))
        pes_2014_waste = max(0, pes_2014_waste)
        
        print("2014年预测结果对比:")
        print(f"基准情景：水质优良率 {base_2014_quality:.1f}%，废水排放 {base_2014_waste:.1f}亿吨")
        print(f"乐观情景：水质优良率 {opt_2014_quality:.1f}%，废水排放 {opt_2014_waste:.1f}亿吨")
        print(f"悲观情景：水质优良率 {pes_2014_quality:.1f}%，废水排放 {pes_2014_waste:.1f}亿吨")
        
        # 风险评估
        print("\n" + "="*60)
        print("风险评估")
        print("="*60)
        
        print("基准情景风险评估:")
        if base_2014_quality < 50:
            print("🔴 极高风险：水质优良率将低于50%")
        elif base_2014_quality < 60:
            print("🟡 高风险：水质优良率将低于60%")
        else:
            print("🟢 中等风险：水质仍有改善空间")
        
        if base_2014_waste > 400:
            print("🔴 极高风险：废水排放将超过400亿吨")
        elif base_2014_waste > 350:
            print("🟡 高风险：废水排放将超过350亿吨")
        else:
            print("🟢 废水排放在可控范围内")
        
        # 关键转折点分析
        print("\n关键转折点分析:")
        
        # 找到水质优良率低于60%的年份
        for pred in predictions:
            if pred['quality'] < 60:
                print(f"⚠️  {pred['year']}年水质优良率将首次低于60% ({pred['quality']:.1f}%)")
                break
        
        # 找到废水排放超过350亿吨的年份
        for pred in predictions:
            if pred['waste'] > 350:
                print(f"⚠️  {pred['year']}年废水排放将首次超过350亿吨 ({pred['waste']:.1f}亿吨)")
                break
        
        # 生成详细报告
        print("\n" + "="*80)
        print("预测分析总结报告")
        print("="*80)
        
        print(f"\n📊 历史趋势 (1995-2004):")
        print(f"   • 水质优良率下降了 {abs(quality_change_total):.1f} 个百分点")
        print(f"   • 废水排放增加了 {waste_change_total:.1f} 亿吨 ({waste_change_total/waste_discharges[0]*100:.1f}%)")
        print(f"   • 年均水质下降 {abs(quality_annual_change):.2f}%")
        print(f"   • 年均废水增长 {waste_annual_change:.2f} 亿吨")
        
        print(f"\n🔮 预测结果 (2005-2014):")
        print(f"   • 基准情景：水质优良率将降至 {base_2014_quality:.1f}%")
        print(f"   • 基准情景：废水排放将增至 {base_2014_waste:.1f} 亿吨")
        print(f"   • 10年水质下降：{base_2014_quality - quality_rates[-1]:.1f} 个百分点")
        print(f"   • 10年废水增长：{base_2014_waste - waste_discharges[-1]:.1f} 亿吨")
        
        print(f"\n💡 主要结论:")
        print("   1. 如不采取有效治理措施，长江水质将持续恶化")
        print("   2. 废水排放量将大幅增加，环境压力持续加大")
        print("   3. 水质优良率可能在2010年前后降至60%以下")
        print("   4. 到2014年，水质优良率可能降至45%左右")
        print("   5. 废水排放量可能增长至400亿吨以上")
        
        print(f"\n🎯 政策建议:")
        print("   1. 立即加强废水处理设施建设和升级改造")
        print("   2. 实施更严格的工业排放标准和监管措施")
        print("   3. 推进流域综合治理，重点治理污染严重区域")
        print("   4. 建立长效监测预警机制，及时发现和处理污染问题")
        print("   5. 加大环保投入，发展清洁生产技术")
        print("   6. 强化跨区域协调，统筹流域治理工作")
        
        # 保存预测结果
        print("\n保存预测结果...")
        
        # 创建预测数据
        prediction_data = []
        
        # 添加历史数据
        for i, year in enumerate(years):
            prediction_data.append({
                '年份': year,
                '类型': '历史数据',
                '水质优良率_%': quality_rates[i],
                '废水排放_亿吨': waste_discharges[i]
            })
        
        # 添加预测数据
        for pred in predictions:
            prediction_data.append({
                '年份': pred['year'],
                '类型': '基准预测',
                '水质优良率_%': pred['quality'],
                '废水排放_亿吨': pred['waste']
            })
        
        # 保存到CSV
        df = pd.DataFrame(prediction_data)
        df.to_csv('yangtze_basic_predictions.csv', index=False, encoding='utf-8-sig')
        
        print("✅ 预测结果已保存到 yangtze_basic_predictions.csv")
        print("✅ 分析完成！")
        
    except FileNotFoundError:
        print("❌ 错误：找不到数据文件 'yangtze_water_quality_1995_2004_summary.csv'")
        print("请确保已运行 water_quality_data_processor.py 生成数据文件")
    except Exception as e:
        print(f"❌ 分析过程中出现错误：{e}")


if __name__ == "__main__":
    main()
