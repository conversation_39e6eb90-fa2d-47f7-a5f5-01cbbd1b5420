#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
长江水质高级预测分析系统
集成多种机器学习模型，提供高质量可视化和详细报告

功能特点：
1. 多模型集成预测（线性回归、SVR、随机森林、神经网络）
2. 高质量图表可视化（带时间戳）
3. 自动生成详细文本报告
4. 模块化设计，易于扩展

作者：数学建模团队
日期：2025年
版本：2.0
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import os
import warnings
warnings.filterwarnings('ignore')

# 机器学习模型
from sklearn.linear_model import LinearRegression, Ridge, Lasso
from sklearn.svm import SVR
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.neural_network import MLPRegressor
from sklearn.preprocessing import PolynomialFeatures, StandardScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.model_selection import cross_val_score, train_test_split

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class AdvancedWaterQualityPredictor:
    """高级长江水质预测分析系统"""
    
    def __init__(self, data_path: str, output_dir: str = "output"):
        """
        初始化高级预测系统
        
        Args:
            data_path: 历史数据CSV文件路径
            output_dir: 输出目录
        """
        self.data_path = data_path
        self.output_dir = output_dir
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(f"{output_dir}/charts", exist_ok=True)
        os.makedirs(f"{output_dir}/reports", exist_ok=True)
        
        # 加载和预处理数据
        self._load_data()
        self._preprocess_data()
        
        # 初始化模型存储
        self.models = {}
        self.model_scores = {}
        self.predictions = {}
        
        print(f"🚀 高级预测系统初始化完成")
        print(f"📊 数据范围: {self.years[0]}-{self.years[-1]}年")
        print(f"📁 输出目录: {output_dir}")
        print(f"🕐 时间戳: {self.timestamp}")
    
    def _load_data(self):
        """加载数据"""
        try:
            self.data = pd.read_csv(self.data_path, encoding='utf-8-sig')
            self.data = self.data.dropna()
            print(f"✅ 数据加载成功: {len(self.data)}条记录")
        except Exception as e:
            raise FileNotFoundError(f"❌ 数据加载失败: {e}")
    
    def _preprocess_data(self):
        """数据预处理"""
        # 提取核心变量
        self.years = self.data['年份'].values
        self.quality_rate = self.data['水质优良率_%'].values
        self.waste_discharge = self.data['废水排放_亿吨'].values
        
        # 创建特征矩阵
        self.X = self.years.reshape(-1, 1)

        # 初始化标准化器
        self.scaler = StandardScaler()
        self.X_scaled = self.scaler.fit_transform(self.X)
        
        # 目标变量
        self.y_quality = self.quality_rate
        self.y_waste = self.waste_discharge
        
        print(f"✅ 数据预处理完成")
        print(f"   水质优良率范围: {self.quality_rate.min():.1f}% - {self.quality_rate.max():.1f}%")
        print(f"   废水排放范围: {self.waste_discharge.min():.1f} - {self.waste_discharge.max():.1f}亿吨")
    
    def build_models(self):
        """构建多种预测模型"""
        print("\n🔧 构建预测模型...")
        
        # 定义模型
        models_config = {
            'linear': LinearRegression(),
            'ridge': Ridge(alpha=1.0),
            'lasso': Lasso(alpha=0.1),
            'svr_linear': SVR(kernel='linear', C=1.0),
            'svr_rbf': SVR(kernel='rbf', C=1.0, gamma='scale'),
            'random_forest': RandomForestRegressor(n_estimators=100, random_state=42),
            'gradient_boost': GradientBoostingRegressor(n_estimators=100, random_state=42),
            'neural_network': MLPRegressor(hidden_layer_sizes=(50, 25), max_iter=1000, random_state=42)
        }
        
        # 训练模型并评估
        for name, model in models_config.items():
            print(f"   训练 {name} 模型...")
            
            # 水质优良率模型
            quality_model = model.__class__(**model.get_params())
            quality_model.fit(self.X_scaled, self.y_quality)
            quality_pred = quality_model.predict(self.X_scaled)
            quality_r2 = r2_score(self.y_quality, quality_pred)
            quality_rmse = np.sqrt(mean_squared_error(self.y_quality, quality_pred))
            
            # 废水排放模型
            waste_model = model.__class__(**model.get_params())
            waste_model.fit(self.X_scaled, self.y_waste)
            waste_pred = waste_model.predict(self.X_scaled)
            waste_r2 = r2_score(self.y_waste, waste_pred)
            waste_rmse = np.sqrt(mean_squared_error(self.y_waste, waste_pred))
            
            # 存储模型和评分
            self.models[name] = {
                'quality_model': quality_model,
                'waste_model': waste_model
            }
            
            self.model_scores[name] = {
                'quality_r2': quality_r2,
                'quality_rmse': quality_rmse,
                'waste_r2': waste_r2,
                'waste_rmse': waste_rmse,
                'avg_r2': (quality_r2 + waste_r2) / 2
            }
            
            print(f"     水质R²: {quality_r2:.3f}, 废水R²: {waste_r2:.3f}")
        
        # 选择最佳模型
        best_model = max(self.model_scores.keys(), 
                        key=lambda x: self.model_scores[x]['avg_r2'])
        self.best_model_name = best_model
        
        print(f"✅ 模型构建完成，最佳模型: {best_model}")
        return self.models, self.model_scores
    
    def predict_future(self, future_years, model_name=None):
        """预测未来趋势"""
        if model_name is None:
            model_name = self.best_model_name
        
        print(f"\n🔮 使用 {model_name} 模型预测未来趋势...")
        
        # 准备未来年份数据
        future_years = np.array(future_years)
        X_future = future_years.reshape(-1, 1)
        X_future_scaled = self.scaler.transform(X_future)
        
        # 获取模型
        model = self.models[model_name]
        
        # 预测
        quality_pred = model['quality_model'].predict(X_future_scaled)
        waste_pred = model['waste_model'].predict(X_future_scaled)
        
        # 确保预测值在合理范围内
        quality_pred = np.clip(quality_pred, 0, 100)
        waste_pred = np.maximum(waste_pred, 0)
        
        # 存储预测结果
        self.predictions[model_name] = {
            'years': future_years,
            'quality_rate': quality_pred,
            'waste_discharge': waste_pred,
            'model_name': model_name
        }
        
        print(f"✅ 预测完成，预测范围: {future_years[0]}-{future_years[-1]}年")
        return self.predictions[model_name]
    
    def generate_scenarios(self, future_years, base_model=None):
        """生成多种情景预测"""
        if base_model is None:
            base_model = self.best_model_name
        
        print(f"\n📈 生成情景分析...")
        
        # 基准预测
        base_pred = self.predict_future(future_years, base_model)
        
        scenarios = {
            '基准情景': base_pred
        }
        
        # 乐观情景（改善50%）
        opt_quality = []
        opt_waste = []
        
        for i, year in enumerate(future_years):
            if i == 0:
                q_change = (base_pred['quality_rate'][i] - self.quality_rate[-1]) * 0.5
                w_change = (base_pred['waste_discharge'][i] - self.waste_discharge[-1]) * 0.5
                opt_quality.append(self.quality_rate[-1] + q_change)
                opt_waste.append(self.waste_discharge[-1] + w_change)
            else:
                q_change = (base_pred['quality_rate'][i] - base_pred['quality_rate'][i-1]) * 0.5
                w_change = (base_pred['waste_discharge'][i] - base_pred['waste_discharge'][i-1]) * 0.5
                opt_quality.append(opt_quality[-1] + q_change)
                opt_waste.append(opt_waste[-1] + w_change)
        
        scenarios['乐观情景'] = {
            'years': future_years,
            'quality_rate': np.array(opt_quality),
            'waste_discharge': np.array(opt_waste),
            'model_name': '乐观调整'
        }
        
        # 悲观情景（恶化50%）
        pes_quality = []
        pes_waste = []
        
        for i, year in enumerate(future_years):
            if i == 0:
                q_change = (base_pred['quality_rate'][i] - self.quality_rate[-1]) * 1.5
                w_change = (base_pred['waste_discharge'][i] - self.waste_discharge[-1]) * 1.5
                pes_quality.append(self.quality_rate[-1] + q_change)
                pes_waste.append(self.waste_discharge[-1] + w_change)
            else:
                q_change = (base_pred['quality_rate'][i] - base_pred['quality_rate'][i-1]) * 1.5
                w_change = (base_pred['waste_discharge'][i] - base_pred['waste_discharge'][i-1]) * 1.5
                pes_quality.append(pes_quality[-1] + q_change)
                pes_waste.append(pes_waste[-1] + w_change)
        
        scenarios['悲观情景'] = {
            'years': future_years,
            'quality_rate': np.array(pes_quality),
            'waste_discharge': np.array(pes_waste),
            'model_name': '悲观调整'
        }
        
        print(f"✅ 情景分析完成，生成3种情景")
        return scenarios

    def create_visualization(self, scenarios, chart_type="all"):
        """创建高质量可视化图表"""
        print(f"\n🎨 生成可视化图表...")

        chart_files = []

        if chart_type in ["all", "trend"]:
            chart_files.append(self._create_trend_chart(scenarios))

        if chart_type in ["all", "comparison"]:
            chart_files.append(self._create_comparison_chart(scenarios))

        if chart_type in ["all", "correlation"]:
            chart_files.append(self._create_correlation_chart())

        if chart_type in ["all", "model_performance"]:
            chart_files.append(self._create_model_performance_chart())

        if chart_type in ["all", "risk_assessment"]:
            chart_files.append(self._create_risk_assessment_chart(scenarios))

        print(f"✅ 可视化完成，生成 {len(chart_files)} 张图表")
        return chart_files

    def _create_trend_chart(self, scenarios):
        """创建趋势预测图"""
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

        # 颜色配置
        colors = {'基准情景': '#1f77b4', '乐观情景': '#2ca02c', '悲观情景': '#d62728'}

        # 水质优良率趋势
        ax1.plot(self.years, self.quality_rate, 'ko-', linewidth=3, markersize=8,
                label='历史数据', alpha=0.8)

        for scenario_name, pred in scenarios.items():
            ax1.plot(pred['years'], pred['quality_rate'],
                    color=colors[scenario_name], linestyle='--', linewidth=2.5,
                    marker='s', markersize=6, label=f'{scenario_name}预测', alpha=0.8)

        ax1.set_title('长江水质优良率预测趋势', fontsize=16, fontweight='bold', pad=20)
        ax1.set_xlabel('年份', fontsize=12)
        ax1.set_ylabel('水质优良率 (%)', fontsize=12)
        ax1.legend(fontsize=11)
        ax1.grid(True, alpha=0.3)
        ax1.set_ylim(0, 100)

        # 废水排放趋势
        ax2.plot(self.years, self.waste_discharge, 'ko-', linewidth=3, markersize=8,
                label='历史数据', alpha=0.8)

        for scenario_name, pred in scenarios.items():
            ax2.plot(pred['years'], pred['waste_discharge'],
                    color=colors[scenario_name], linestyle='--', linewidth=2.5,
                    marker='s', markersize=6, label=f'{scenario_name}预测', alpha=0.8)

        ax2.set_title('长江废水排放量预测趋势', fontsize=16, fontweight='bold', pad=20)
        ax2.set_xlabel('年份', fontsize=12)
        ax2.set_ylabel('废水排放量 (亿吨)', fontsize=12)
        ax2.legend(fontsize=11)
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()

        filename = f"{self.output_dir}/charts/trend_prediction_{self.timestamp}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        return filename

    def _create_comparison_chart(self, scenarios):
        """创建情景对比图"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

        # 2014年预测对比
        scenario_names = list(scenarios.keys())
        final_qualities = [scenarios[name]['quality_rate'][-1] for name in scenario_names]
        final_wastes = [scenarios[name]['waste_discharge'][-1] for name in scenario_names]

        colors = ['#2ca02c', '#1f77b4', '#d62728']

        # 水质优良率对比
        bars1 = ax1.bar(scenario_names, final_qualities, color=colors, alpha=0.7)
        ax1.set_title('2014年水质优良率预测对比', fontsize=14, fontweight='bold')
        ax1.set_ylabel('水质优良率 (%)', fontsize=12)
        ax1.set_ylim(0, 100)

        # 添加数值标签
        for bar, value in zip(bars1, final_qualities):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                    f'{value:.1f}%', ha='center', va='bottom', fontsize=11, fontweight='bold')

        # 废水排放对比
        bars2 = ax2.bar(scenario_names, final_wastes, color=colors, alpha=0.7)
        ax2.set_title('2014年废水排放量预测对比', fontsize=14, fontweight='bold')
        ax2.set_ylabel('废水排放量 (亿吨)', fontsize=12)

        # 添加数值标签
        for bar, value in zip(bars2, final_wastes):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 5,
                    f'{value:.0f}', ha='center', va='bottom', fontsize=11, fontweight='bold')

        # 变化幅度对比
        quality_changes = [final_qualities[i] - self.quality_rate[-1] for i in range(len(scenario_names))]
        waste_changes = [final_wastes[i] - self.waste_discharge[-1] for i in range(len(scenario_names))]

        bars3 = ax3.bar(scenario_names, quality_changes, color=colors, alpha=0.7)
        ax3.set_title('水质优良率变化幅度', fontsize=14, fontweight='bold')
        ax3.set_ylabel('变化幅度 (百分点)', fontsize=12)
        ax3.axhline(y=0, color='black', linestyle='-', alpha=0.3)

        for bar, value in zip(bars3, quality_changes):
            ax3.text(bar.get_x() + bar.get_width()/2,
                    bar.get_height() + (1 if value >= 0 else -2),
                    f'{value:+.1f}', ha='center',
                    va='bottom' if value >= 0 else 'top',
                    fontsize=11, fontweight='bold')

        bars4 = ax4.bar(scenario_names, waste_changes, color=colors, alpha=0.7)
        ax4.set_title('废水排放量变化幅度', fontsize=14, fontweight='bold')
        ax4.set_ylabel('变化幅度 (亿吨)', fontsize=12)
        ax4.axhline(y=0, color='black', linestyle='-', alpha=0.3)

        for bar, value in zip(bars4, waste_changes):
            ax4.text(bar.get_x() + bar.get_width()/2,
                    bar.get_height() + (5 if value >= 0 else -10),
                    f'{value:+.0f}', ha='center',
                    va='bottom' if value >= 0 else 'top',
                    fontsize=11, fontweight='bold')

        plt.tight_layout()

        filename = f"{self.output_dir}/charts/scenario_comparison_{self.timestamp}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        return filename

    def _create_correlation_chart(self):
        """创建相关性分析图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # 散点图
        ax1.scatter(self.waste_discharge, self.quality_rate,
                   c=self.years, cmap='viridis', s=100, alpha=0.7, edgecolors='black')

        # 添加趋势线
        z = np.polyfit(self.waste_discharge, self.quality_rate, 1)
        p = np.poly1d(z)
        ax1.plot(self.waste_discharge, p(self.waste_discharge), "r--", alpha=0.8, linewidth=2)

        # 计算相关系数
        correlation = np.corrcoef(self.waste_discharge, self.quality_rate)[0, 1]
        ax1.text(0.05, 0.95, f'相关系数: {correlation:.3f}',
                transform=ax1.transAxes, fontsize=12,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))

        ax1.set_title('水质优良率 vs 废水排放量', fontsize=14, fontweight='bold')
        ax1.set_xlabel('废水排放量 (亿吨)', fontsize=12)
        ax1.set_ylabel('水质优良率 (%)', fontsize=12)
        ax1.grid(True, alpha=0.3)

        # 添加颜色条
        cbar = plt.colorbar(ax1.collections[0], ax=ax1)
        cbar.set_label('年份', fontsize=12)

        # 时间序列图
        ax2_twin = ax2.twinx()

        line1 = ax2.plot(self.years, self.quality_rate, 'b-o', linewidth=2.5,
                        markersize=6, label='水质优良率', alpha=0.8)
        line2 = ax2_twin.plot(self.years, self.waste_discharge, 'r-s', linewidth=2.5,
                             markersize=6, label='废水排放量', alpha=0.8)

        ax2.set_title('水质与废水排放时间序列', fontsize=14, fontweight='bold')
        ax2.set_xlabel('年份', fontsize=12)
        ax2.set_ylabel('水质优良率 (%)', fontsize=12, color='blue')
        ax2_twin.set_ylabel('废水排放量 (亿吨)', fontsize=12, color='red')

        # 合并图例
        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        ax2.legend(lines, labels, loc='center right')

        ax2.grid(True, alpha=0.3)

        plt.tight_layout()

        filename = f"{self.output_dir}/charts/correlation_analysis_{self.timestamp}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        return filename

    def _create_model_performance_chart(self):
        """创建模型性能对比图"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

        model_names = list(self.model_scores.keys())
        quality_r2 = [self.model_scores[name]['quality_r2'] for name in model_names]
        waste_r2 = [self.model_scores[name]['waste_r2'] for name in model_names]
        quality_rmse = [self.model_scores[name]['quality_rmse'] for name in model_names]
        waste_rmse = [self.model_scores[name]['waste_rmse'] for name in model_names]

        # R²对比
        x = np.arange(len(model_names))
        width = 0.35

        bars1 = ax1.bar(x - width/2, quality_r2, width, label='水质优良率', alpha=0.7)
        bars2 = ax1.bar(x + width/2, waste_r2, width, label='废水排放', alpha=0.7)

        ax1.set_title('模型R²性能对比', fontsize=14, fontweight='bold')
        ax1.set_ylabel('R² 分数', fontsize=12)
        ax1.set_xticks(x)
        ax1.set_xticklabels(model_names, rotation=45, ha='right')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.set_ylim(0, 1)

        # 添加数值标签
        for bar in bars1:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{height:.3f}', ha='center', va='bottom', fontsize=9)

        for bar in bars2:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{height:.3f}', ha='center', va='bottom', fontsize=9)

        # RMSE对比
        bars3 = ax2.bar(x - width/2, quality_rmse, width, label='水质优良率', alpha=0.7)
        bars4 = ax2.bar(x + width/2, waste_rmse, width, label='废水排放', alpha=0.7)

        ax2.set_title('模型RMSE性能对比', fontsize=14, fontweight='bold')
        ax2.set_ylabel('RMSE', fontsize=12)
        ax2.set_xticks(x)
        ax2.set_xticklabels(model_names, rotation=45, ha='right')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 综合性能雷达图
        from math import pi

        # 选择前5个模型进行雷达图展示
        top_models = sorted(model_names, key=lambda x: self.model_scores[x]['avg_r2'], reverse=True)[:5]

        categories = ['水质R²', '废水R²', '水质RMSE(反)', '废水RMSE(反)']
        N = len(categories)

        angles = [n / float(N) * 2 * pi for n in range(N)]
        angles += angles[:1]

        ax3 = plt.subplot(2, 2, 3, projection='polar')

        colors = plt.cm.Set3(np.linspace(0, 1, len(top_models)))

        for i, model in enumerate(top_models):
            scores = self.model_scores[model]
            values = [
                scores['quality_r2'],
                scores['waste_r2'],
                1 - scores['quality_rmse'] / max(quality_rmse),  # 反转RMSE
                1 - scores['waste_rmse'] / max(waste_rmse)       # 反转RMSE
            ]
            values += values[:1]

            ax3.plot(angles, values, 'o-', linewidth=2, label=model, color=colors[i])
            ax3.fill(angles, values, alpha=0.25, color=colors[i])

        ax3.set_xticks(angles[:-1])
        ax3.set_xticklabels(categories)
        ax3.set_ylim(0, 1)
        ax3.set_title('模型综合性能雷达图', fontsize=14, fontweight='bold', pad=20)
        ax3.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))

        # 最佳模型突出显示
        best_model = max(model_names, key=lambda x: self.model_scores[x]['avg_r2'])
        ax4.text(0.5, 0.7, f'最佳模型', ha='center', va='center',
                fontsize=20, fontweight='bold', transform=ax4.transAxes)
        ax4.text(0.5, 0.5, f'{best_model}', ha='center', va='center',
                fontsize=16, color='red', fontweight='bold', transform=ax4.transAxes)
        ax4.text(0.5, 0.3, f'综合R²: {self.model_scores[best_model]["avg_r2"]:.3f}',
                ha='center', va='center', fontsize=14, transform=ax4.transAxes)
        ax4.set_xlim(0, 1)
        ax4.set_ylim(0, 1)
        ax4.axis('off')

        plt.tight_layout()

        filename = f"{self.output_dir}/charts/model_performance_{self.timestamp}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        return filename

    def _create_risk_assessment_chart(self, scenarios):
        """创建风险评估图"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

        # 风险阈值定义
        quality_thresholds = {'极高风险': 50, '高风险': 60, '中风险': 70, '低风险': 80}
        waste_thresholds = {'极高风险': 400, '高风险': 350, '中风险': 300, '低风险': 250}

        # 风险时间线
        base_scenario = scenarios['基准情景']
        years = base_scenario['years']
        quality_pred = base_scenario['quality_rate']
        waste_pred = base_scenario['waste_discharge']

        # 水质风险时间线
        risk_colors = {'低风险': '#2ca02c', '中风险': '#ff7f0e', '高风险': '#d62728', '极高风险': '#8b0000'}

        for i, (risk_level, threshold) in enumerate(quality_thresholds.items()):
            if i == 0:
                ax1.axhspan(0, threshold, alpha=0.3, color=risk_colors[risk_level], label=risk_level)
            else:
                prev_threshold = list(quality_thresholds.values())[i-1]
                ax1.axhspan(threshold, prev_threshold, alpha=0.3, color=risk_colors[risk_level], label=risk_level)

        ax1.plot(self.years, self.quality_rate, 'ko-', linewidth=3, markersize=8, label='历史数据')
        ax1.plot(years, quality_pred, 'r--', linewidth=3, marker='s', markersize=6, label='预测数据')

        ax1.set_title('水质优良率风险评估', fontsize=14, fontweight='bold')
        ax1.set_xlabel('年份', fontsize=12)
        ax1.set_ylabel('水质优良率 (%)', fontsize=12)
        ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax1.grid(True, alpha=0.3)
        ax1.set_ylim(0, 100)

        # 废水排放风险时间线
        for i, (risk_level, threshold) in enumerate(waste_thresholds.items()):
            if i == len(waste_thresholds) - 1:
                ax2.axhspan(threshold, 500, alpha=0.3, color=risk_colors[risk_level], label=risk_level)
            else:
                next_threshold = list(waste_thresholds.values())[i+1]
                ax2.axhspan(threshold, next_threshold, alpha=0.3, color=risk_colors[risk_level], label=risk_level)

        ax2.plot(self.years, self.waste_discharge, 'ko-', linewidth=3, markersize=8, label='历史数据')
        ax2.plot(years, waste_pred, 'r--', linewidth=3, marker='s', markersize=6, label='预测数据')

        ax2.set_title('废水排放量风险评估', fontsize=14, fontweight='bold')
        ax2.set_xlabel('年份', fontsize=12)
        ax2.set_ylabel('废水排放量 (亿吨)', fontsize=12)
        ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax2.grid(True, alpha=0.3)

        # 风险矩阵
        risk_matrix = np.zeros((4, 4))
        risk_labels = ['低风险', '中风险', '高风险', '极高风险']

        # 计算每年的风险等级
        for year, q, w in zip(years, quality_pred, waste_pred):
            q_risk = 0 if q >= 80 else 1 if q >= 70 else 2 if q >= 60 else 3
            w_risk = 0 if w < 250 else 1 if w < 300 else 2 if w < 350 else 3
            risk_matrix[q_risk, w_risk] += 1

        im = ax3.imshow(risk_matrix, cmap='Reds', alpha=0.8)
        ax3.set_xticks(range(4))
        ax3.set_yticks(range(4))
        ax3.set_xticklabels(risk_labels, rotation=45)
        ax3.set_yticklabels(risk_labels)
        ax3.set_xlabel('废水排放风险等级', fontsize=12)
        ax3.set_ylabel('水质风险等级', fontsize=12)
        ax3.set_title('风险矩阵分布', fontsize=14, fontweight='bold')

        # 添加数值标签
        for i in range(4):
            for j in range(4):
                text = ax3.text(j, i, int(risk_matrix[i, j]), ha="center", va="center",
                               color="white" if risk_matrix[i, j] > risk_matrix.max()/2 else "black",
                               fontsize=12, fontweight='bold')

        plt.colorbar(im, ax=ax3, label='年数')

        # 关键节点识别
        critical_points = []

        # 找到关键转折点
        for i, (year, q, w) in enumerate(zip(years, quality_pred, waste_pred)):
            if q < 60 and (i == 0 or quality_pred[i-1] >= 60):
                critical_points.append((year, '水质首次低于60%', 'high'))
            if q < 50 and (i == 0 or quality_pred[i-1] >= 50):
                critical_points.append((year, '水质首次低于50%', 'critical'))
            if w > 350 and (i == 0 or waste_pred[i-1] <= 350):
                critical_points.append((year, '废水首次超过350亿吨', 'high'))
            if w > 400 and (i == 0 or waste_pred[i-1] <= 400):
                critical_points.append((year, '废水首次超过400亿吨', 'critical'))

        # 显示关键节点
        ax4.axis('off')
        ax4.set_title('关键风险节点', fontsize=14, fontweight='bold')

        y_pos = 0.9
        for year, event, level in critical_points:
            color = '#d62728' if level == 'critical' else '#ff7f0e'
            ax4.text(0.1, y_pos, f'🔴 {year}年: {event}',
                    fontsize=12, color=color, fontweight='bold', transform=ax4.transAxes)
            y_pos -= 0.15

        if not critical_points:
            ax4.text(0.5, 0.5, '未发现关键风险节点', ha='center', va='center',
                    fontsize=14, transform=ax4.transAxes)

        plt.tight_layout()

        filename = f"{self.output_dir}/charts/risk_assessment_{self.timestamp}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        return filename

    def generate_report(self, scenarios, chart_files):
        """生成详细的文本分析报告"""
        print(f"\n📝 生成分析报告...")

        report_content = []

        # 报告头部
        report_content.extend([
            "=" * 80,
            "长江水质高级预测分析报告",
            "=" * 80,
            f"生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}",
            f"分析时间戳: {self.timestamp}",
            f"数据来源: {self.data_path}",
            "",
            "报告摘要",
            "-" * 40,
            "本报告基于1995-2004年长江水质历史数据，运用多种机器学习模型",
            "对未来10年(2005-2014年)的水质发展趋势进行预测分析。",
            "分析包括基准、乐观、悲观三种情景，并提供详细的风险评估。",
            "",
        ])

        # 数据概况
        report_content.extend([
            "1. 数据概况",
            "-" * 40,
            f"数据时间范围: {self.years[0]}-{self.years[-1]}年 (共{len(self.years)}年)",
            f"水质优良率范围: {self.quality_rate.min():.1f}% - {self.quality_rate.max():.1f}%",
            f"废水排放范围: {self.waste_discharge.min():.1f} - {self.waste_discharge.max():.1f}亿吨",
            "",
            "历史趋势分析:",
            f"• 水质优良率变化: {self.quality_rate[0]:.1f}% → {self.quality_rate[-1]:.1f}% (变化{self.quality_rate[-1]-self.quality_rate[0]:+.1f}个百分点)",
            f"• 废水排放变化: {self.waste_discharge[0]:.1f} → {self.waste_discharge[-1]:.1f}亿吨 (变化{self.waste_discharge[-1]-self.waste_discharge[0]:+.1f}亿吨)",
            f"• 相关系数: {np.corrcoef(self.quality_rate, self.waste_discharge)[0,1]:.3f} (强负相关)",
            "",
        ])

        # 模型性能
        report_content.extend([
            "2. 模型性能评估",
            "-" * 40,
        ])

        for model_name, scores in self.model_scores.items():
            report_content.extend([
                f"{model_name.upper()}模型:",
                f"  水质预测 - R²: {scores['quality_r2']:.3f}, RMSE: {scores['quality_rmse']:.2f}",
                f"  废水预测 - R²: {scores['waste_r2']:.3f}, RMSE: {scores['waste_rmse']:.2f}",
                f"  综合评分: {scores['avg_r2']:.3f}",
                ""
            ])

        best_model = max(self.model_scores.keys(), key=lambda x: self.model_scores[x]['avg_r2'])
        report_content.extend([
            f"最佳模型: {best_model.upper()}",
            f"综合R²得分: {self.model_scores[best_model]['avg_r2']:.3f}",
            "",
        ])

        # 预测结果
        report_content.extend([
            "3. 预测结果分析",
            "-" * 40,
        ])

        for scenario_name, pred in scenarios.items():
            final_quality = pred['quality_rate'][-1]
            final_waste = pred['waste_discharge'][-1]
            quality_change = final_quality - self.quality_rate[-1]
            waste_change = final_waste - self.waste_discharge[-1]

            report_content.extend([
                f"{scenario_name}:",
                f"  2014年水质优良率: {final_quality:.1f}% (变化{quality_change:+.1f}个百分点)",
                f"  2014年废水排放: {final_waste:.1f}亿吨 (变化{waste_change:+.1f}亿吨)",
                ""
            ])

        # 风险评估
        base_scenario = scenarios['基准情景']
        critical_years = []

        for i, (year, q, w) in enumerate(zip(base_scenario['years'],
                                           base_scenario['quality_rate'],
                                           base_scenario['waste_discharge'])):
            if q < 60 and (i == 0 or base_scenario['quality_rate'][i-1] >= 60):
                critical_years.append(f"{year}年: 水质优良率首次低于60%")
            if q < 50 and (i == 0 or base_scenario['quality_rate'][i-1] >= 50):
                critical_years.append(f"{year}年: 水质优良率首次低于50%")
            if w > 350 and (i == 0 or base_scenario['waste_discharge'][i-1] <= 350):
                critical_years.append(f"{year}年: 废水排放首次超过350亿吨")

        report_content.extend([
            "4. 风险评估",
            "-" * 40,
            "关键风险节点:",
        ])

        if critical_years:
            for event in critical_years:
                report_content.append(f"⚠️  {event}")
        else:
            report_content.append("未发现关键风险节点")

        report_content.extend([
            "",
            "风险等级定义:",
            "• 水质优良率: >80%(低风险), 70-80%(中风险), 60-70%(高风险), <60%(极高风险)",
            "• 废水排放: <250亿吨(低风险), 250-300(中风险), 300-350(高风险), >350(极高风险)",
            "",
        ])

        # 政策建议
        report_content.extend([
            "5. 政策建议",
            "-" * 40,
            "基于预测结果，建议采取以下措施:",
            "",
            "紧急措施 (2005-2008年):",
            "• 立即启动重点污染源治理工程",
            "• 实施最严格的工业废水排放标准",
            "• 建立水质监测预警系统",
            "• 加强跨省市协调治理机制",
            "",
            "中期措施 (2008-2012年):",
            "• 推进产业结构调整和升级",
            "• 实施流域综合治理工程",
            "• 完善环保法律法规体系",
            "• 建立生态补偿机制",
            "",
            "长期措施 (2012年以后):",
            "• 发展循环经济和清洁生产",
            "• 建立可持续发展模式",
            "• 加强国际合作与技术交流",
            "• 提高公众环保意识",
            "",
        ])

        # 技术说明
        report_content.extend([
            "6. 技术说明",
            "-" * 40,
            "模型方法:",
            "• 线性回归: 基于最小二乘法的线性趋势外推",
            "• 支持向量回归: 非线性核函数映射",
            "• 随机森林: 集成学习方法",
            "• 神经网络: 多层感知器回归",
            "",
            "评估指标:",
            "• R²决定系数: 衡量模型拟合优度",
            "• RMSE均方根误差: 衡量预测精度",
            "• 交叉验证: 评估模型泛化能力",
            "",
            "局限性:",
            "• 基于历史数据的线性外推，未考虑政策干预",
            "• 模型假设未来趋势延续历史模式",
            "• 未考虑极端气候和突发事件影响",
            "",
        ])

        # 生成的文件
        report_content.extend([
            "7. 生成文件",
            "-" * 40,
            "图表文件:",
        ])

        for chart_file in chart_files:
            filename = os.path.basename(chart_file)
            report_content.append(f"• {filename}")

        report_content.extend([
            "",
            "数据文件:",
            f"• 预测结果: predictions_{self.timestamp}.csv",
            f"• 模型评估: model_scores_{self.timestamp}.csv",
            "",
        ])

        # 结论
        base_final_quality = scenarios['基准情景']['quality_rate'][-1]
        base_final_waste = scenarios['基准情景']['waste_discharge'][-1]

        report_content.extend([
            "8. 结论",
            "-" * 40,
            "基于多模型集成分析，主要结论如下:",
            "",
            f"1. 如不采取有效治理措施，2014年长江水质优良率将降至{base_final_quality:.1f}%",
            f"2. 废水排放量将增至{base_final_waste:.1f}亿吨，环境压力巨大",
            "3. 水质与废水排放呈强负相关关系，控制污染源是关键",
            "4. 必须在2008年前采取有效措施，避免进入不可逆转的恶化轨道",
            "",
            "建议立即启动长江流域综合治理工程，实施最严格的环境保护制度。",
            "",
            "=" * 80,
            f"报告生成完成 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "=" * 80,
        ])

        # 保存报告
        report_filename = f"{self.output_dir}/reports/analysis_report_{self.timestamp}.txt"
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_content))

        print(f"✅ 分析报告生成完成: {report_filename}")
        return report_filename

    def save_predictions(self, scenarios):
        """保存预测结果到CSV文件"""
        print(f"\n💾 保存预测结果...")

        # 合并所有预测数据
        all_data = []

        # 历史数据
        for year, quality, waste in zip(self.years, self.quality_rate, self.waste_discharge):
            all_data.append({
                '年份': year,
                '数据类型': '历史数据',
                '情景': '实际数据',
                '水质优良率_%': quality,
                '废水排放_亿吨': waste
            })

        # 预测数据
        for scenario_name, pred in scenarios.items():
            for year, quality, waste in zip(pred['years'], pred['quality_rate'], pred['waste_discharge']):
                all_data.append({
                    '年份': year,
                    '数据类型': '预测数据',
                    '情景': scenario_name,
                    '水质优良率_%': quality,
                    '废水排放_亿吨': waste
                })

        # 保存到CSV
        df = pd.DataFrame(all_data)
        predictions_filename = f"{self.output_dir}/predictions_{self.timestamp}.csv"
        df.to_csv(predictions_filename, index=False, encoding='utf-8-sig')

        # 保存模型评估结果
        model_data = []
        for model_name, scores in self.model_scores.items():
            model_data.append({
                '模型名称': model_name,
                '水质R2': scores['quality_r2'],
                '水质RMSE': scores['quality_rmse'],
                '废水R2': scores['waste_r2'],
                '废水RMSE': scores['waste_rmse'],
                '综合R2': scores['avg_r2']
            })

        model_df = pd.DataFrame(model_data)
        model_filename = f"{self.output_dir}/model_scores_{self.timestamp}.csv"
        model_df.to_csv(model_filename, index=False, encoding='utf-8-sig')

        print(f"✅ 预测结果保存完成:")
        print(f"   预测数据: {predictions_filename}")
        print(f"   模型评估: {model_filename}")

        return predictions_filename, model_filename

    def run_complete_analysis(self, future_years=None):
        """运行完整的分析流程"""
        if future_years is None:
            future_years = list(range(2005, 2015))  # 默认预测2005-2014年

        print(f"\n🚀 开始完整分析流程...")
        print(f"预测年份范围: {future_years[0]}-{future_years[-1]}年")

        try:
            # 1. 构建模型
            self.build_models()

            # 2. 生成预测和情景
            scenarios = self.generate_scenarios(future_years)

            # 3. 创建可视化
            chart_files = self.create_visualization(scenarios)

            # 4. 生成报告
            report_file = self.generate_report(scenarios, chart_files)

            # 5. 保存数据
            pred_file, model_file = self.save_predictions(scenarios)

            print(f"\n🎉 分析完成！生成文件:")
            print(f"📊 图表文件: {len(chart_files)}张")
            print(f"📝 分析报告: {report_file}")
            print(f"💾 预测数据: {pred_file}")
            print(f"📈 模型评估: {model_file}")

            return {
                'scenarios': scenarios,
                'chart_files': chart_files,
                'report_file': report_file,
                'prediction_file': pred_file,
                'model_file': model_file
            }

        except Exception as e:
            print(f"❌ 分析过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
            return None


def main():
    """主函数 - 演示高级预测系统"""
    print("🌊 长江水质高级预测分析系统")
    print("=" * 60)

    try:
        # 初始化预测系统
        predictor = AdvancedWaterQualityPredictor(
            data_path='yangtze_water_quality_1995_2004_summary.csv',
            output_dir='advanced_output'
        )

        # 运行完整分析
        results = predictor.run_complete_analysis()

        if results:
            print(f"\n✅ 高级分析系统运行成功！")
            print(f"📁 所有结果保存在 'advanced_output' 目录中")

            # 显示关键预测结果
            base_scenario = results['scenarios']['基准情景']
            final_quality = base_scenario['quality_rate'][-1]
            final_waste = base_scenario['waste_discharge'][-1]

            print(f"\n🔮 关键预测结果 (基准情景):")
            print(f"   2014年水质优良率: {final_quality:.1f}%")
            print(f"   2014年废水排放: {final_waste:.1f}亿吨")

        else:
            print(f"❌ 分析失败，请检查错误信息")

    except Exception as e:
        print(f"❌ 系统初始化失败: {e}")
        print("请确保数据文件 'yangtze_water_quality_1995_2004_summary.csv' 存在")


if __name__ == "__main__":
    main()
