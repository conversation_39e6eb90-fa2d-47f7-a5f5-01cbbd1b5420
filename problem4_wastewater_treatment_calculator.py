#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
长江水质问题4解决方案：污水处理量计算器

问题4：根据预测分析，如果未来10年内每年都要求长江干流的IV类和V类水的比例控制在20%以内，
且没有劣V类水，那么每年需要处理多少污水？

作者：水质分析系统
日期：2025-01-29
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import os
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class WastewaterTreatmentCalculator:
    """污水处理量计算器"""
    
    def __init__(self):
        """初始化计算器"""
        self.data_long = None
        self.mainstream_data = None
        self.prediction_years = list(range(2005, 2015))  # 未来10年
        self.target_iv_v_ratio = 20.0  # IV类+V类水比例目标：≤20%
        self.target_inferior_v_ratio = 0.0  # 劣V类水比例目标：0%
        
        # 水环境参数
        self.degradation_coefficient = 0.2  # 降解系数 (1/天)
        self.annual_flow = 9000  # 年均流量 (亿立方米)
        self.treatment_efficiency = 0.9  # 污水处理效率 (90%)
        
        # 污染物浓度标准 (mg/L)
        self.water_quality_standards = {
            'Ⅰ类': {'COD': 2, 'NH3N': 0.15},
            'Ⅱ类': {'COD': 4, 'NH3N': 0.5},
            'Ⅲ类': {'COD': 6, 'NH3N': 1.0},
            'Ⅳ类': {'COD': 10, 'NH3N': 1.5},
            'Ⅴ类': {'COD': 15, 'NH3N': 2.0},
            '劣Ⅴ类': {'COD': 25, 'NH3N': 3.0}  # 假设值
        }
        
    def load_data(self):
        """加载历史数据"""
        try:
            self.data_long = pd.read_csv('yangtze_water_quality_1995_2004_long.csv')
            print("✅ 成功加载历史水质数据")
            
            # 筛选干流数据
            self.mainstream_data = self.data_long[
                (self.data_long['范围'] == '干流') & 
                (self.data_long['时段'] == '水文年')
            ].copy()
            
            if self.mainstream_data.empty:
                # 如果没有水文年数据，使用枯水期数据作为代表
                self.mainstream_data = self.data_long[
                    (self.data_long['范围'] == '干流') & 
                    (self.data_long['时段'] == '枯水期')
                ].copy()
                print("⚠️  使用枯水期数据代表年均情况")
            
            print(f"📊 干流数据记录数：{len(self.mainstream_data)}")
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败：{e}")
            return False
    
    def analyze_historical_trends(self):
        """分析历史水质等级变化趋势"""
        print("\n" + "="*60)
        print("📈 历史水质等级趋势分析")
        print("="*60)
        
        # 按年份和水质等级汇总
        yearly_stats = self.mainstream_data.groupby(['年份', '水质等级'])['百分比_%'].mean().reset_index()
        yearly_pivot = yearly_stats.pivot(index='年份', columns='水质等级', values='百分比_%').fillna(0)
        
        # 确保所有水质等级都存在
        grade_order = ['Ⅰ类', 'Ⅱ类', 'Ⅲ类', 'Ⅳ类', 'Ⅴ类', '劣Ⅴ类']
        for grade in grade_order:
            if grade not in yearly_pivot.columns:
                yearly_pivot[grade] = 0
        
        yearly_pivot = yearly_pivot[grade_order]
        
        # 计算关键指标
        yearly_pivot['IV_V_类'] = yearly_pivot['Ⅳ类'] + yearly_pivot['Ⅴ类']
        yearly_pivot['优良率'] = yearly_pivot['Ⅰ类'] + yearly_pivot['Ⅱ类'] + yearly_pivot['Ⅲ类']
        
        print("干流水质等级历史变化：")
        print(yearly_pivot.round(1))
        
        # 分析趋势
        years = yearly_pivot.index.values
        trends = {}

        print(f"\n趋势分析：")
        for column in ['IV_V_类', '劣Ⅴ类', '优良率']:
            if len(years) > 1:
                try:
                    slope = np.polyfit(years, yearly_pivot[column], 1)[0]
                    trends[column] = slope
                    print(f"  {column}年均变化趋势：{slope:+.2f}%/年")
                except Exception as e:
                    print(f"  {column}趋势计算失败：{e}")
                    trends[column] = 0
            else:
                trends[column] = 0
                print(f"  {column}：数据不足，无法计算趋势")

        self.historical_trends = trends
        self.yearly_pivot = yearly_pivot

        print(f"✅ 历史趋势分析完成")
        return yearly_pivot
    
    def predict_future_water_quality(self):
        """预测未来水质等级分布"""
        print("\n" + "="*60)
        print("🔮 未来水质等级分布预测")
        print("="*60)
        
        years = self.yearly_pivot.index.values
        predictions = {}
        
        # 为每个水质等级建立预测模型
        grade_order = ['Ⅰ类', 'Ⅱ类', 'Ⅲ类', 'Ⅳ类', 'Ⅴ类', '劣Ⅴ类']
        
        for grade in grade_order:
            if grade in self.yearly_pivot.columns:
                y_values = self.yearly_pivot[grade].values
                
                # 线性回归预测
                if len(years) > 1:
                    slope, intercept = np.polyfit(years, y_values, 1)
                    
                    # 预测未来10年
                    future_values = []
                    for year in self.prediction_years:
                        predicted_value = slope * year + intercept
                        # 确保预测值在合理范围内
                        predicted_value = max(0, min(100, predicted_value))
                        future_values.append(predicted_value)
                    
                    predictions[grade] = {
                        'slope': slope,
                        'intercept': intercept,
                        'values': future_values,
                        'r2': r2_score(y_values, slope * years + intercept) if len(years) > 2 else 0
                    }
                else:
                    # 如果数据不足，使用最后一年的值
                    last_value = y_values[-1] if len(y_values) > 0 else 0
                    predictions[grade] = {
                        'slope': 0,
                        'intercept': last_value,
                        'values': [last_value] * len(self.prediction_years),
                        'r2': 0
                    }
        
        # 标准化预测结果，确保总和为100%
        for i, year in enumerate(self.prediction_years):
            total = sum(predictions[grade]['values'][i] for grade in grade_order)
            if total > 0:
                for grade in grade_order:
                    predictions[grade]['values'][i] = predictions[grade]['values'][i] / total * 100
        
        self.water_quality_predictions = predictions
        
        # 显示预测结果
        print("\n未来10年水质等级分布预测（%）：")
        prediction_df = pd.DataFrame({
            grade: predictions[grade]['values'] 
            for grade in grade_order
        }, index=self.prediction_years)
        
        prediction_df['IV_V_类'] = prediction_df['Ⅳ类'] + prediction_df['Ⅴ类']
        prediction_df['优良率'] = prediction_df['Ⅰ类'] + prediction_df['Ⅱ类'] + prediction_df['Ⅲ类']
        
        print(prediction_df.round(1))
        
        self.prediction_df = prediction_df
        return prediction_df
    
    def calculate_pollution_load(self, water_quality_distribution):
        """计算污染负荷"""
        # 基于水质等级分布计算加权平均污染物浓度
        total_cod = 0
        total_nh3n = 0
        
        for grade, percentage in water_quality_distribution.items():
            if grade in self.water_quality_standards:
                weight = percentage / 100
                total_cod += self.water_quality_standards[grade]['COD'] * weight
                total_nh3n += self.water_quality_standards[grade]['NH3N'] * weight
        
        # 计算年污染负荷 (万吨/年)
        cod_load = total_cod * self.annual_flow * 10  # mg/L * 亿m³ * 10 = 万吨
        nh3n_load = total_nh3n * self.annual_flow * 10
        
        return {'COD': cod_load, 'NH3N': nh3n_load}

    def calculate_target_water_quality(self):
        """计算目标水质分布"""
        print("\n" + "="*60)
        print("🎯 目标水质分布计算")
        print("="*60)

        target_distributions = []

        for i, year in enumerate(self.prediction_years):
            # 获取预测的水质分布
            predicted = {
                grade: self.prediction_df.loc[year, grade]
                for grade in ['Ⅰ类', 'Ⅱ类', 'Ⅲ类', 'Ⅳ类', 'Ⅴ类', '劣Ⅴ类']
            }

            # 计算当前IV类+V类比例和劣V类比例
            current_iv_v = predicted['Ⅳ类'] + predicted['Ⅴ类']
            current_inferior_v = predicted['劣Ⅴ类']

            # 计算需要改善的比例
            excess_iv_v = max(0, current_iv_v - self.target_iv_v_ratio)
            excess_inferior_v = current_inferior_v

            # 目标分布：将超标的水质改善到III类
            target = predicted.copy()
            target['劣Ⅴ类'] = 0  # 消除劣V类

            # 如果IV+V类超标，将超标部分改善到III类
            if excess_iv_v > 0:
                # 按比例减少IV类和V类
                if current_iv_v > 0:
                    iv_ratio = predicted['Ⅳ类'] / current_iv_v
                    v_ratio = predicted['Ⅴ类'] / current_iv_v

                    target['Ⅳ类'] = predicted['Ⅳ类'] - excess_iv_v * iv_ratio
                    target['Ⅴ类'] = predicted['Ⅴ类'] - excess_iv_v * v_ratio
                else:
                    target['Ⅳ类'] = predicted['Ⅳ类']
                    target['Ⅴ类'] = predicted['Ⅴ类']

            # 将改善的水质分配到III类
            improved_percentage = excess_iv_v + excess_inferior_v
            target['Ⅲ类'] = predicted['Ⅲ类'] + improved_percentage

            # 确保总和为100%
            total = sum(target.values())
            if total > 0:
                for grade in target:
                    target[grade] = target[grade] / total * 100

            target_distributions.append({
                'year': year,
                'predicted': predicted,
                'target': target,
                'improvement_needed': improved_percentage
            })

        self.target_distributions = target_distributions

        # 显示目标vs预测对比
        print("\n目标水质分布 vs 预测分布对比：")
        for dist in target_distributions[:5]:  # 显示前5年
            year = dist['year']
            pred_iv_v = dist['predicted']['Ⅳ类'] + dist['predicted']['Ⅴ类']
            target_iv_v = dist['target']['Ⅳ类'] + dist['target']['Ⅴ类']

            print(f"{year}年：")
            print(f"  预测IV+V类: {pred_iv_v:.1f}% → 目标IV+V类: {target_iv_v:.1f}%")
            print(f"  预测劣V类: {dist['predicted']['劣Ⅴ类']:.1f}% → 目标劣V类: {dist['target']['劣Ⅴ类']:.1f}%")
            print(f"  需要改善: {dist['improvement_needed']:.1f}%")

        return target_distributions

    def calculate_wastewater_treatment_volume(self):
        """计算每年需要处理的污水量"""
        print("\n" + "="*60)
        print("💧 污水处理量计算")
        print("="*60)

        treatment_results = []

        for dist in self.target_distributions:
            year = dist['year']
            predicted = dist['predicted']
            target = dist['target']

            # 计算预测和目标的污染负荷
            predicted_load = self.calculate_pollution_load(predicted)
            target_load = self.calculate_pollution_load(target)

            # 计算需要减少的污染负荷
            cod_reduction = predicted_load['COD'] - target_load['COD']
            nh3n_reduction = predicted_load['NH3N'] - target_load['NH3N']

            # 计算需要处理的污水量
            # 假设污水中COD浓度为300mg/L，氨氮浓度为30mg/L（典型城市污水）
            wastewater_cod_conc = 300  # mg/L
            wastewater_nh3n_conc = 30  # mg/L

            # 基于COD计算需要处理的污水量
            if cod_reduction > 0:
                # 考虑处理效率
                required_cod_removal = cod_reduction / self.treatment_efficiency
                wastewater_volume_cod = required_cod_removal / (wastewater_cod_conc * 10) * 10000  # 亿吨
            else:
                wastewater_volume_cod = 0

            # 基于氨氮计算需要处理的污水量
            if nh3n_reduction > 0:
                required_nh3n_removal = nh3n_reduction / self.treatment_efficiency
                wastewater_volume_nh3n = required_nh3n_removal / (wastewater_nh3n_conc * 10) * 10000  # 亿吨
            else:
                wastewater_volume_nh3n = 0

            # 取较大值作为最终需要处理的污水量
            wastewater_volume = max(wastewater_volume_cod, wastewater_volume_nh3n)

            treatment_results.append({
                'year': year,
                'predicted_cod_load': predicted_load['COD'],
                'target_cod_load': target_load['COD'],
                'cod_reduction_needed': cod_reduction,
                'predicted_nh3n_load': predicted_load['NH3N'],
                'target_nh3n_load': target_load['NH3N'],
                'nh3n_reduction_needed': nh3n_reduction,
                'wastewater_volume_cod': wastewater_volume_cod,
                'wastewater_volume_nh3n': wastewater_volume_nh3n,
                'wastewater_volume_total': wastewater_volume,
                'improvement_percentage': dist['improvement_needed']
            })

        self.treatment_results = treatment_results

        # 显示计算结果
        print("\n年度污水处理量计算结果：")
        print("-" * 80)
        print(f"{'年份':<6} {'需改善比例':<10} {'COD削减':<10} {'氨氮削减':<10} {'处理量':<10}")
        print(f"{'':^6} {'(%)':<10} {'(万吨)':<10} {'(万吨)':<10} {'(亿吨)':<10}")
        print("-" * 80)

        total_volume = 0
        for result in treatment_results:
            print(f"{result['year']:<6} {result['improvement_percentage']:<10.1f} "
                  f"{result['cod_reduction_needed']:<10.1f} {result['nh3n_reduction_needed']:<10.1f} "
                  f"{result['wastewater_volume_total']:<10.1f}")
            total_volume += result['wastewater_volume_total']

        print("-" * 80)
        print(f"{'总计':<6} {'':<10} {'':<10} {'':<10} {total_volume:<10.1f}")
        print(f"{'平均':<6} {'':<10} {'':<10} {'':<10} {total_volume/len(treatment_results):<10.1f}")

        return treatment_results

    def create_visualizations(self, output_dir):
        """创建可视化图表"""
        print("\n" + "="*60)
        print("📊 生成可视化图表")
        print("="*60)

        # 创建输出目录
        charts_dir = os.path.join(output_dir, 'charts')
        os.makedirs(charts_dir, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 图表1：年度污水处理量需求
        plt.figure(figsize=(12, 8))

        years = [r['year'] for r in self.treatment_results]
        volumes = [r['wastewater_volume_total'] for r in self.treatment_results]
        improvements = [r['improvement_percentage'] for r in self.treatment_results]

        # 主图：污水处理量
        ax1 = plt.subplot(2, 1, 1)
        bars = plt.bar(years, volumes, color='steelblue', alpha=0.7, edgecolor='navy')
        plt.title('未来10年每年需要处理的污水量', fontsize=16, fontweight='bold', pad=20)
        plt.xlabel('年份', fontsize=12)
        plt.ylabel('污水处理量 (亿吨)', fontsize=12)
        plt.grid(True, alpha=0.3)

        # 添加数值标签
        for bar, volume in zip(bars, volumes):
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{volume:.1f}', ha='center', va='bottom', fontsize=10)

        # 子图：需要改善的水质比例
        ax2 = plt.subplot(2, 1, 2)
        plt.plot(years, improvements, 'ro-', linewidth=2, markersize=6)
        plt.title('需要改善的水质比例', fontsize=14, fontweight='bold')
        plt.xlabel('年份', fontsize=12)
        plt.ylabel('需改善比例 (%)', fontsize=12)
        plt.grid(True, alpha=0.3)

        # 添加目标线
        plt.axhline(y=20, color='red', linestyle='--', alpha=0.7, label='IV+V类目标线(20%)')
        plt.legend()

        plt.tight_layout()
        chart1_path = os.path.join(charts_dir, f'wastewater_treatment_volume_{timestamp}.png')
        plt.savefig(chart1_path, dpi=300, bbox_inches='tight')
        plt.close()

        # 图表2：水质改善效果对比
        plt.figure(figsize=(14, 10))

        # 选择几个关键年份进行对比
        key_years = [2005, 2008, 2011, 2014]
        key_data = [r for r in self.target_distributions if r['year'] in key_years]

        n_years = len(key_data)
        fig, axes = plt.subplots(2, n_years, figsize=(16, 10))

        grade_order = ['Ⅰ类', 'Ⅱ类', 'Ⅲ类', 'Ⅳ类', 'Ⅴ类', '劣Ⅴ类']
        colors = ['#2E8B57', '#32CD32', '#FFD700', '#FF8C00', '#FF4500', '#8B0000']

        for i, data in enumerate(key_data):
            year = data['year']
            predicted = [data['predicted'][grade] for grade in grade_order]
            target = [data['target'][grade] for grade in grade_order]

            # 预测分布
            axes[0, i].pie(predicted, labels=grade_order, colors=colors, autopct='%1.1f%%',
                          startangle=90, textprops={'fontsize': 8})
            axes[0, i].set_title(f'{year}年预测分布', fontsize=12, fontweight='bold')

            # 目标分布
            axes[1, i].pie(target, labels=grade_order, colors=colors, autopct='%1.1f%%',
                          startangle=90, textprops={'fontsize': 8})
            axes[1, i].set_title(f'{year}年目标分布', fontsize=12, fontweight='bold')

        plt.suptitle('水质分布对比：预测 vs 目标', fontsize=16, fontweight='bold')
        plt.tight_layout()
        chart2_path = os.path.join(charts_dir, f'water_quality_comparison_{timestamp}.png')
        plt.savefig(chart2_path, dpi=300, bbox_inches='tight')
        plt.close()

        # 图表3：污染负荷削减分析
        plt.figure(figsize=(12, 8))

        cod_reductions = [r['cod_reduction_needed'] for r in self.treatment_results]
        nh3n_reductions = [r['nh3n_reduction_needed'] for r in self.treatment_results]

        x = np.arange(len(years))
        width = 0.35

        plt.bar(x - width/2, cod_reductions, width, label='COD削减量', color='lightcoral', alpha=0.8)
        plt.bar(x + width/2, nh3n_reductions, width, label='氨氮削减量', color='lightblue', alpha=0.8)

        plt.title('年度污染负荷削减需求', fontsize=16, fontweight='bold')
        plt.xlabel('年份', fontsize=12)
        plt.ylabel('削减量 (万吨)', fontsize=12)
        plt.xticks(x, years, rotation=45)
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()

        chart3_path = os.path.join(charts_dir, f'pollution_load_reduction_{timestamp}.png')
        plt.savefig(chart3_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✅ 图表已保存：")
        print(f"   - 污水处理量需求：{chart1_path}")
        print(f"   - 水质分布对比：{chart2_path}")
        print(f"   - 污染负荷削减：{chart3_path}")

        return [chart1_path, chart2_path, chart3_path]

    def generate_report(self, output_dir):
        """生成详细分析报告"""
        print("\n" + "="*60)
        print("📝 生成分析报告")
        print("="*60)

        # 创建报告目录
        reports_dir = os.path.join(output_dir, 'reports')
        os.makedirs(reports_dir, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = os.path.join(reports_dir, f'problem4_analysis_report_{timestamp}.txt')

        # 计算统计数据
        total_volume = sum(r['wastewater_volume_total'] for r in self.treatment_results)
        avg_volume = total_volume / len(self.treatment_results)
        max_volume = max(r['wastewater_volume_total'] for r in self.treatment_results)
        max_year = [r['year'] for r in self.treatment_results if r['wastewater_volume_total'] == max_volume][0]

        total_cod_reduction = sum(r['cod_reduction_needed'] for r in self.treatment_results)
        total_nh3n_reduction = sum(r['nh3n_reduction_needed'] for r in self.treatment_results)

        # 生成报告内容
        report_content = [
            "="*80,
            "长江水质问题4分析报告：污水处理量计算",
            "="*80,
            "",
            "问题描述：",
            "根据预测分析，如果未来10年内每年都要求长江干流的IV类和V类水的比例",
            "控制在20%以内，且没有劣V类水，那么每年需要处理多少污水？",
            "",
            "="*80,
            "1. 分析方法",
            "="*80,
            "",
            "1.1 技术路线",
            "• 基于1995-2004年历史数据建立水质等级分布预测模型",
            "• 设定目标水质标准：IV类+V类≤20%，劣V类=0%",
            "• 计算预测水质与目标水质的差距",
            "• 基于污染负荷平衡原理计算所需污水处理量",
            "",
            "1.2 关键参数",
            f"• 年均流量：{self.annual_flow}亿立方米",
            f"• 降解系数：{self.degradation_coefficient}/天",
            f"• 处理效率：{self.treatment_efficiency*100}%",
            "• 典型污水浓度：COD 300mg/L，氨氮 30mg/L",
            "",
            "1.3 水质标准",
            "• I类：COD≤2mg/L，氨氮≤0.15mg/L",
            "• II类：COD≤4mg/L，氨氮≤0.5mg/L",
            "• III类：COD≤6mg/L，氨氮≤1.0mg/L",
            "• IV类：COD≤10mg/L，氨氮≤1.5mg/L",
            "• V类：COD≤15mg/L，氨氮≤2.0mg/L",
            "",
            "="*80,
            "2. 计算结果",
            "="*80,
            "",
            "2.1 总体统计",
            f"• 10年总处理量：{total_volume:.1f}亿吨",
            f"• 年均处理量：{avg_volume:.1f}亿吨",
            f"• 最大年处理量：{max_volume:.1f}亿吨（{max_year}年）",
            f"• 总COD削减量：{total_cod_reduction:.1f}万吨",
            f"• 总氨氮削减量：{total_nh3n_reduction:.1f}万吨",
            "",
            "2.2 年度处理量明细",
            "-" * 80,
            f"{'年份':<6} {'需改善':<8} {'COD削减':<10} {'氨氮削减':<10} {'处理量':<10} {'累计处理量':<12}",
            f"{'':^6} {'比例(%)':<8} {'(万吨)':<10} {'(万吨)':<10} {'(亿吨)':<10} {'(亿吨)':<12}",
            "-" * 80,
        ]

        cumulative_volume = 0
        for result in self.treatment_results:
            cumulative_volume += result['wastewater_volume_total']
            report_content.append(
                f"{result['year']:<6} {result['improvement_percentage']:<8.1f} "
                f"{result['cod_reduction_needed']:<10.1f} {result['nh3n_reduction_needed']:<10.1f} "
                f"{result['wastewater_volume_total']:<10.1f} {cumulative_volume:<12.1f}"
            )

        report_content.extend([
            "-" * 80,
            "",
            "="*80,
            "3. 关键发现",
            "="*80,
            "",
        ])

        # 分析关键发现
        if max_volume > 50:
            report_content.append("⚠️  高处理量需求：部分年份需要处理超过50亿吨污水")

        if avg_volume > 20:
            report_content.append("⚠️  持续高负荷：年均处理量超过20亿吨")

        # 趋势分析
        early_avg = np.mean([r['wastewater_volume_total'] for r in self.treatment_results[:3]])
        late_avg = np.mean([r['wastewater_volume_total'] for r in self.treatment_results[-3:]])

        if late_avg > early_avg * 1.2:
            report_content.append("📈 处理量呈上升趋势：后期处理需求显著增加")
        elif late_avg < early_avg * 0.8:
            report_content.append("📉 处理量呈下降趋势：后期处理需求有所减少")
        else:
            report_content.append("➡️  处理量相对稳定：各年份处理需求基本持平")

        report_content.extend([
            "",
            "="*80,
            "4. 政策建议",
            "="*80,
            "",
            "4.1 基础设施建设",
            f"• 规划建设年处理能力{avg_volume:.0f}亿吨的污水处理设施",
            f"• 重点关注{max_year}年等高峰年份的处理能力保障",
            "• 优先建设高效脱氮除磷工艺的处理设施",
            "",
            "4.2 分阶段实施策略",
            "• 第一阶段（2005-2007年）：重点消除劣V类水质",
            "• 第二阶段（2008-2011年）：控制IV类和V类水质比例",
            "• 第三阶段（2012-2014年）：巩固治理成果，提升整体水质",
            "",
            "4.3 技术路径",
            "• 采用先进的生物脱氮除磷技术",
            "• 推广膜生物反应器(MBR)等高效处理工艺",
            "• 建设人工湿地等生态处理设施",
            "• 实施污水资源化利用",
            "",
            "4.4 管理措施",
            "• 建立流域统一的污水处理标准",
            "• 实施严格的排放许可制度",
            "• 加强污水处理设施运行监管",
            "• 建立长效的资金保障机制",
            "",
            "="*80,
            "5. 模型局限性",
            "="*80,
            "",
            "• 基于历史趋势的线性外推，未考虑政策干预效果",
            "• 污水处理效率假设为固定值，实际可能有所变化",
            "• 未考虑季节性变化和极端气候事件影响",
            "• 污染物浓度标准基于国标，实际情况可能有差异",
            "",
            "="*80,
            "6. 结论",
            "="*80,
            "",
            f"要实现长江干流IV类和V类水比例控制在20%以内、无劣V类水的目标，",
            f"未来10年需要累计处理{total_volume:.1f}亿吨污水，年均{avg_volume:.1f}亿吨。",
            f"这需要大规模的污水处理基础设施建设和持续的资金投入。",
            "",
            f"建议采用分阶段、分区域的治理策略，优先消除劣V类水质，",
            f"逐步控制IV类和V类水质比例，最终实现长江水质的根本改善。",
            "",
            "="*80,
            f"报告生成时间：{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}",
            "="*80,
        ])

        # 保存报告
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_content))

        print(f"✅ 分析报告已保存：{report_path}")
        return report_path

    def run_analysis(self):
        """运行完整分析"""
        print("🌊 长江水质问题4分析：污水处理量计算")
        print("="*80)
        print("问题：如果未来10年内每年都要求长江干流的IV类和V类水的比例")
        print("控制在20%以内，且没有劣V类水，那么每年需要处理多少污水？")
        print("="*80)

        # 1. 加载数据
        if not self.load_data():
            return False

        # 2. 分析历史趋势
        self.analyze_historical_trends()

        # 3. 预测未来水质
        self.predict_future_water_quality()

        # 4. 计算目标水质
        self.calculate_target_water_quality()

        # 5. 计算污水处理量
        self.calculate_wastewater_treatment_volume()

        # 6. 创建输出目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = f"problem4_output_{timestamp}"
        os.makedirs(output_dir, exist_ok=True)

        # 7. 生成可视化图表
        chart_paths = self.create_visualizations(output_dir)

        # 8. 生成分析报告
        report_path = self.generate_report(output_dir)

        # 9. 总结
        print("\n" + "="*80)
        print("📋 分析完成总结")
        print("="*80)

        total_volume = sum(r['wastewater_volume_total'] for r in self.treatment_results)
        avg_volume = total_volume / len(self.treatment_results)

        print(f"✅ 核心结论：")
        print(f"   • 10年总处理量：{total_volume:.1f}亿吨")
        print(f"   • 年均处理量：{avg_volume:.1f}亿吨")
        print(f"   • 处理量范围：{min(r['wastewater_volume_total'] for r in self.treatment_results):.1f} - "
              f"{max(r['wastewater_volume_total'] for r in self.treatment_results):.1f}亿吨")

        print(f"\n📁 输出文件：")
        print(f"   • 输出目录：{output_dir}")
        print(f"   • 分析报告：{report_path}")
        for i, chart_path in enumerate(chart_paths, 1):
            print(f"   • 图表{i}：{chart_path}")

        print(f"\n💡 关键建议：")
        print(f"   • 需要大规模污水处理基础设施建设")
        print(f"   • 建议采用分阶段治理策略")
        print(f"   • 优先消除劣V类水质")
        print(f"   • 逐步控制IV类和V类水质比例")

        return True


def main():
    """主函数"""
    try:
        # 创建计算器实例
        calculator = WastewaterTreatmentCalculator()

        # 运行分析
        success = calculator.run_analysis()

        if success:
            print("\n🎉 问题4分析成功完成！")
        else:
            print("\n❌ 分析过程中出现错误")

    except Exception as e:
        print(f"\n💥 程序运行出错：{e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
