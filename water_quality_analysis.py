#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
长江流域水质数据分析和可视化
基于1995-2004年水质报告数据

作者：数学建模团队
日期：2025年
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib import rcParams
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class WaterQualityAnalyzer:
    """长江流域水质数据分析器"""
    
    def __init__(self, csv_path_long: str, csv_path_summary: str):
        """
        初始化分析器
        
        Args:
            csv_path_long: 长表格式CSV文件路径
            csv_path_summary: 汇总统计CSV文件路径
        """
        self.df_long = pd.read_csv(csv_path_long, encoding='utf-8-sig')
        self.df_summary = pd.read_csv(csv_path_summary, encoding='utf-8-sig')
        
        # 数据预处理
        self.df_long['年份'] = self.df_long['年份'].astype(int)
        self.df_summary['年份'] = self.df_summary['年份'].astype(int)
        
    def plot_water_quality_trend(self, save_path: str = None):
        """
        绘制水质优良率趋势图
        
        Args:
            save_path: 保存路径
        """
        plt.figure(figsize=(12, 8))
        
        # 主图：水质优良率趋势
        plt.subplot(2, 2, 1)
        plt.plot(self.df_summary['年份'], self.df_summary['水质优良率_%'], 
                marker='o', linewidth=2, markersize=6, color='#2E8B57')
        plt.title('长江流域水质优良率变化趋势 (1995-2004)', fontsize=14, fontweight='bold')
        plt.xlabel('年份', fontsize=12)
        plt.ylabel('水质优良率 (%)', fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.ylim(60, 95)
        
        # 添加趋势线
        z = np.polyfit(self.df_summary['年份'], self.df_summary['水质优良率_%'], 1)
        p = np.poly1d(z)
        plt.plot(self.df_summary['年份'], p(self.df_summary['年份']), 
                "--", alpha=0.8, color='red', label=f'趋势线 (斜率: {z[0]:.2f})')
        plt.legend()
        
        # 废水排放趋势
        plt.subplot(2, 2, 2)
        plt.plot(self.df_summary['年份'], self.df_summary['废水排放_亿吨'], 
                marker='s', linewidth=2, markersize=6, color='#DC143C')
        plt.title('废水排放量变化趋势', fontsize=14, fontweight='bold')
        plt.xlabel('年份', fontsize=12)
        plt.ylabel('废水排放量 (亿吨)', fontsize=12)
        plt.grid(True, alpha=0.3)
        
        # 总流量趋势
        plt.subplot(2, 2, 3)
        plt.plot(self.df_summary['年份'], self.df_summary['总流量_亿立方米'], 
                marker='^', linewidth=2, markersize=6, color='#4169E1')
        plt.title('长江总流量变化趋势', fontsize=14, fontweight='bold')
        plt.xlabel('年份', fontsize=12)
        plt.ylabel('总流量 (亿立方米)', fontsize=12)
        plt.grid(True, alpha=0.3)
        
        # 水质与废水排放相关性
        plt.subplot(2, 2, 4)
        plt.scatter(self.df_summary['废水排放_亿吨'], self.df_summary['水质优良率_%'], 
                   s=80, alpha=0.7, color='#FF6347')
        
        # 添加相关系数
        corr = np.corrcoef(self.df_summary['废水排放_亿吨'], self.df_summary['水质优良率_%'])[0, 1]
        plt.title(f'水质优良率 vs 废水排放量\n相关系数: {corr:.3f}', fontsize=14, fontweight='bold')
        plt.xlabel('废水排放量 (亿吨)', fontsize=12)
        plt.ylabel('水质优良率 (%)', fontsize=12)
        plt.grid(True, alpha=0.3)
        
        # 添加年份标签
        for i, year in enumerate(self.df_summary['年份']):
            plt.annotate(str(year), 
                        (self.df_summary['废水排放_亿吨'].iloc[i], 
                         self.df_summary['水质优良率_%'].iloc[i]),
                        xytext=(5, 5), textcoords='offset points', fontsize=9)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_water_grade_distribution(self, save_path: str = None):
        """
        绘制水质等级分布图
        
        Args:
            save_path: 保存路径
        """
        # 计算各年份各水质等级的比例
        yearly_grade = self.df_long.groupby(['年份', '水质等级'])['百分比_%'].mean().reset_index()
        yearly_grade_pivot = yearly_grade.pivot(index='年份', columns='水质等级', values='百分比_%')
        
        # 确保列的顺序
        grade_order = ['Ⅰ类', 'Ⅱ类', 'Ⅲ类', 'Ⅳ类', 'Ⅴ类', '劣Ⅴ类']
        yearly_grade_pivot = yearly_grade_pivot.reindex(columns=grade_order, fill_value=0)
        
        plt.figure(figsize=(14, 10))
        
        # 堆积面积图
        plt.subplot(2, 1, 1)
        colors = ['#228B22', '#32CD32', '#90EE90', '#FFD700', '#FF8C00', '#DC143C']
        plt.stackplot(yearly_grade_pivot.index, 
                     yearly_grade_pivot['Ⅰ类'], yearly_grade_pivot['Ⅱ类'], 
                     yearly_grade_pivot['Ⅲ类'], yearly_grade_pivot['Ⅳ类'], 
                     yearly_grade_pivot['Ⅴ类'], yearly_grade_pivot['劣Ⅴ类'],
                     labels=grade_order, colors=colors, alpha=0.8)
        
        plt.title('长江流域水质等级分布变化 (1995-2004)', fontsize=16, fontweight='bold')
        plt.xlabel('年份', fontsize=12)
        plt.ylabel('比例 (%)', fontsize=12)
        plt.legend(loc='upper right', bbox_to_anchor=(1.15, 1))
        plt.grid(True, alpha=0.3)
        
        # 热力图
        plt.subplot(2, 1, 2)
        sns.heatmap(yearly_grade_pivot.T, annot=True, fmt='.1f', cmap='RdYlGn_r', 
                   cbar_kws={'label': '比例 (%)'})
        plt.title('水质等级分布热力图', fontsize=16, fontweight='bold')
        plt.xlabel('年份', fontsize=12)
        plt.ylabel('水质等级', fontsize=12)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_regional_comparison(self, save_path: str = None):
        """
        绘制区域对比图
        
        Args:
            save_path: 保存路径
        """
        # 计算各区域的水质优良率
        good_grades = ['Ⅰ类', 'Ⅱ类', 'Ⅲ类']
        regional_data = []
        
        for year in sorted(self.df_long['年份'].unique()):
            year_data = self.df_long[self.df_long['年份'] == year]
            
            for region in ['全流域', '干流', '支流']:
                region_data = year_data[year_data['范围'] == region]
                total_length = region_data['河长_km'].sum()
                good_length = region_data[region_data['水质等级'].isin(good_grades)]['河长_km'].sum()
                good_ratio = (good_length / total_length * 100) if total_length > 0 else 0
                
                regional_data.append({
                    '年份': year,
                    '范围': region,
                    '水质优良率_%': good_ratio
                })
        
        regional_df = pd.DataFrame(regional_data)
        
        plt.figure(figsize=(14, 8))
        
        # 线图对比
        plt.subplot(1, 2, 1)
        for region in ['全流域', '干流', '支流']:
            region_data = regional_df[regional_df['范围'] == region]
            plt.plot(region_data['年份'], region_data['水质优良率_%'], 
                    marker='o', linewidth=2, label=region, markersize=6)
        
        plt.title('各区域水质优良率对比', fontsize=14, fontweight='bold')
        plt.xlabel('年份', fontsize=12)
        plt.ylabel('水质优良率 (%)', fontsize=12)
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 箱线图
        plt.subplot(1, 2, 2)
        regional_pivot = regional_df.pivot(index='年份', columns='范围', values='水质优良率_%')
        regional_pivot.boxplot(ax=plt.gca())
        plt.title('各区域水质优良率分布', fontsize=14, fontweight='bold')
        plt.ylabel('水质优良率 (%)', fontsize=12)
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def generate_statistical_summary(self):
        """生成统计摘要"""
        print("长江流域水质数据统计摘要 (1995-2004)")
        print("=" * 50)
        
        # 基本统计
        print("\n1. 基本统计信息:")
        print(f"   数据年份范围: {self.df_summary['年份'].min()}-{self.df_summary['年份'].max()}")
        print(f"   水质优良率: {self.df_summary['水质优良率_%'].min():.1f}% - {self.df_summary['水质优良率_%'].max():.1f}%")
        print(f"   平均水质优良率: {self.df_summary['水质优良率_%'].mean():.1f}%")
        print(f"   废水排放量: {self.df_summary['废水排放_亿吨'].min():.1f} - {self.df_summary['废水排放_亿吨'].max():.1f} 亿吨")
        print(f"   总流量: {self.df_summary['总流量_亿立方米'].min():.1f} - {self.df_summary['总流量_亿立方米'].max():.1f} 亿立方米")
        
        # 趋势分析
        print("\n2. 趋势分析:")
        quality_trend = np.polyfit(self.df_summary['年份'], self.df_summary['水质优良率_%'], 1)[0]
        waste_trend = np.polyfit(self.df_summary['年份'], self.df_summary['废水排放_亿吨'], 1)[0]
        
        print(f"   水质优良率年均变化: {quality_trend:.2f}%/年 ({'下降' if quality_trend < 0 else '上升'})")
        print(f"   废水排放年均变化: {waste_trend:.2f}亿吨/年 ({'减少' if waste_trend < 0 else '增加'})")
        
        # 相关性分析
        print("\n3. 相关性分析:")
        corr_quality_waste = np.corrcoef(self.df_summary['废水排放_亿吨'], self.df_summary['水质优良率_%'])[0, 1]
        corr_quality_flow = np.corrcoef(self.df_summary['总流量_亿立方米'], self.df_summary['水质优良率_%'])[0, 1]
        
        print(f"   水质优良率 vs 废水排放: {corr_quality_waste:.3f} ({'负相关' if corr_quality_waste < 0 else '正相关'})")
        print(f"   水质优良率 vs 总流量: {corr_quality_flow:.3f} ({'负相关' if corr_quality_flow < 0 else '正相关'})")
        
        # 关键发现
        print("\n4. 关键发现:")
        worst_year = self.df_summary.loc[self.df_summary['水质优良率_%'].idxmin(), '年份']
        best_year = self.df_summary.loc[self.df_summary['水质优良率_%'].idxmax(), '年份']
        max_waste_year = self.df_summary.loc[self.df_summary['废水排放_亿吨'].idxmax(), '年份']
        
        print(f"   水质最好年份: {best_year}年 ({self.df_summary['水质优良率_%'].max():.1f}%)")
        print(f"   水质最差年份: {worst_year}年 ({self.df_summary['水质优良率_%'].min():.1f}%)")
        print(f"   废水排放最多年份: {max_waste_year}年 ({self.df_summary['废水排放_亿吨'].max():.1f}亿吨)")
        
        total_decline = self.df_summary['水质优良率_%'].iloc[-1] - self.df_summary['水质优良率_%'].iloc[0]
        print(f"   10年间水质优良率变化: {total_decline:.1f}个百分点")


def main():
    """主函数"""
    print("长江流域水质数据分析")
    print("=" * 30)
    
    # 创建分析器
    analyzer = WaterQualityAnalyzer(
        'yangtze_water_quality_1995_2004_long.csv',
        'yangtze_water_quality_1995_2004_summary.csv'
    )
    
    # 生成统计摘要
    analyzer.generate_statistical_summary()
    
    # 生成图表
    print("\n正在生成图表...")
    analyzer.plot_water_quality_trend('water_quality_trend.png')
    analyzer.plot_water_grade_distribution('water_grade_distribution.png')
    analyzer.plot_regional_comparison('regional_comparison.png')
    
    print("分析完成！图表已保存。")


if __name__ == "__main__":
    main()
